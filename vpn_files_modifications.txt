VPN设置脚本文件修改详细内容
===============================

1. /etc/ipsec.conf - IPsec主配置文件
----------------------------------------
修改内容：
- 完全重写文件
- 添加config setup段：
  * ikev1-policy=accept
  * virtual-private网络配置
  * uniqueids=no
- 添加conn shared段：
  * 加密算法配置（AES256/128-SHA2/SHA1）
  * 生命周期设置（24小时）
  * DPD配置（30秒检测，300秒超时）
- 添加conn l2tp-psk段：
  * L2TP传输模式配置
  * 端口17/1701配置
- 添加conn xauth-psk段：
  * XAUTH客户端配置
  * IP地址池分配
  * DNS服务器配置
  * Cisco Unity支持

2. /etc/ipsec.secrets - IPsec预共享密钥文件
------------------------------------------
修改内容：
- 完全重写文件
- 添加PSK配置：%any %any : PSK "$VPN_IPSEC_PSK"

3. /etc/xl2tpd/xl2tpd.conf - xl2tpd配置文件
------------------------------------------
修改内容：
- 完全重写文件
- 添加[global]段：
  * port = 1701
- 添加[lns default]段：
  * ip range = $L2TP_POOL (默认*************-**************)
  * local ip = $L2TP_LOCAL (默认************)
  * require chap = yes
  * refuse pap = yes
  * require authentication = yes
  * name = l2tpd
  * pppoptfile = /etc/ppp/options.xl2tpd
  * length bit = yes

4. /etc/ppp/options.xl2tpd - PPP选项文件
--------------------------------------
修改内容：
- 完全重写文件
- 添加PPP配置：
  * +mschap-v2 (启用MS-CHAPv2认证)
  * ipcp-accept-local/remote (接受IP配置)
  * noccp (禁用压缩)
  * auth (要求认证)
  * mtu 1280 / mru 1280 (设置MTU/MRU)
  * proxyarp (启用代理ARP)
  * lcp-echo-failure 4 / lcp-echo-interval 30 (心跳检测)
  * connect-delay 5000 (连接延迟)
  * ms-dns $DNS_SRV1 (主DNS，默认*******)
  * ms-dns $DNS_SRV2 (备用DNS，默认*******)

5. /etc/ppp/chap-secrets - CHAP认证密钥文件
-----------------------------------------
修改内容：
- 完全重写文件
- 添加用户认证：
  * "$VPN_USER" l2tpd "$VPN_PASSWORD" *

6. /etc/ipsec.d/passwd - IPsec用户密码文件
----------------------------------------
修改内容：
- 完全重写文件
- 添加XAUTH用户：
  * $VPN_USER:$VPN_PASSWORD_ENC:xauth-psk

7. /etc/sysctl.conf - 内核参数配置
---------------------------------
修改内容：
- 追加内容（不覆盖原有配置）
- 添加标识注释：# Added by hwdsl2 VPN script
- 添加内核消息队列配置：
  * kernel.msgmnb = 65536
  * kernel.msgmax = 65536
- 添加IPv4网络配置：
  * net.ipv4.ip_forward = 1 (启用IP转发)
  * net.ipv4.conf.all.accept_redirects = 0
  * net.ipv4.conf.all.send_redirects = 0
  * net.ipv4.conf.all.rp_filter = 0
  * net.ipv4.conf.default.accept_redirects = 0
  * net.ipv4.conf.default.send_redirects = 0
  * net.ipv4.conf.default.rp_filter = 0
  * net.ipv4.conf.$NET_IFACE.send_redirects = 0
  * net.ipv4.conf.$NET_IFACE.rp_filter = 0
- 添加网络性能优化：
  * net.core.wmem_max = 16777216
  * net.core.rmem_max = 16777216
  * net.ipv4.tcp_rmem = 4096 87380 16777216
  * net.ipv4.tcp_wmem = 4096 87380 16777216
- 如果支持BBR，添加：
  * net.core.default_qdisc = fq
  * net.ipv4.tcp_congestion_control = bbr

8. /etc/sysconfig/iptables - IPTables规则文件
-------------------------------------------
修改内容（仅在非nftables系统）：
- 完全重写文件
- 添加标识注释：# Modified by hwdsl2 VPN script
- 添加INPUT规则：
  * 丢弃未受IPsec保护的L2TP流量
  * 丢弃无效连接状态的包
  * 允许已建立和相关的连接
  * 允许UDP 500,4500端口（IKE和NAT-T）
  * 允许受IPsec保护的L2TP流量
- 添加FORWARD规则：
  * 允许PPP接口和外网接口间的流量转发
  * 允许XAUTH网段的流量转发
- 添加NAT规则：
  * 对L2TP和XAUTH网段进行MASQUERADE

9. /etc/sysconfig/nftables.conf - NFTables规则文件
------------------------------------------------
修改内容（仅在nftables系统）：
- 完全重写文件
- 添加标识注释：# Modified by hwdsl2 VPN script
- 添加flush ruleset命令
- 复制当前nftables规则集
- 通过nft命令动态添加VPN相关规则

10. /etc/fail2ban/jail.local - Fail2Ban配置
-----------------------------------------
修改内容（仅在文件不存在时创建）：
- 创建新文件
- 添加[ssh-iptables]段：
  * enabled = true
  * filter = sshd
  * logpath = /var/log/secure
  * port = ssh
- 根据防火墙类型添加：
  * nftables系统：banaction = nftables-multiport[blocktype=drop]
  * iptables系统：action = iptables[name=SSH, port=ssh, protocol=tcp]

11. /etc/rc.local - 开机启动脚本
-------------------------------
修改内容：
- 如果文件不存在，创建并添加shebang：#!/bin/sh
- 追加内容（不覆盖原有内容）
- 添加标识注释：# Added by hwdsl2 VPN script
- 添加启动脚本：
  * (sleep 15
  * service ipsec restart
  * service xl2tpd restart
  * echo 1 > /proc/sys/net/ipv4/ip_forward)&

12. /etc/firewalld/firewalld.conf - Firewalld配置
------------------------------------------------
修改内容（仅在nftables系统且配置存在时）：
- 修改NftablesTableOwner设置：
  * 将NftablesTableOwner=yes改为NftablesTableOwner=no

13. /etc/dhcp/dhclient.conf - DHCP客户端配置
------------------------------------------
修改内容（仅在Google Cloud Platform环境）：
- 在"send host-name"行后添加：
  * interface "$NET_IFACE" {
  * default interface-mtu 1500;
  * supersede interface-mtu 1500;
  * }

14. /etc/crypto-policies/back-ends/nss.config - NSS加密策略
--------------------------------------------------------
修改内容（仅在文件存在且不包含SHA1时）：
- 修改"ALL allow="行：
  * 在allow=后添加SHA1:支持

15. /opt/src/ - 源码目录
-----------------------
修改内容：
- 创建目录（如果不存在）
- 下载并解压Libreswan源码
- 下载辅助脚本

16. /opt/src/ikev2.sh - IKEv2管理脚本
----------------------------------
修改内容：
- 从GitHub下载ikev2setup.sh并重命名为ikev2.sh
- 设置可执行权限
- 创建到/usr/bin的符号链接

17. /opt/src/addvpnuser.sh - 添加VPN用户脚本
------------------------------------------
修改内容：
- 从GitHub下载add_vpn_user.sh并重命名为addvpnuser.sh
- 设置可执行权限
- 创建到/usr/bin的符号链接

18. /opt/src/delvpnuser.sh - 删除VPN用户脚本
------------------------------------------
修改内容：
- 从GitHub下载del_vpn_user.sh并重命名为delvpnuser.sh
- 设置可执行权限
- 创建到/usr/bin的符号链接

19. /usr/bin/ikev2.sh - IKEv2脚本符号链接
---------------------------------------
修改内容：
- 创建指向/opt/src/ikev2.sh的符号链接

20. /usr/bin/addvpnuser.sh - 添加用户脚本符号链接
----------------------------------------------
修改内容：
- 创建指向/opt/src/addvpnuser.sh的符号链接

21. /usr/bin/delvpnuser.sh - 删除用户脚本符号链接
----------------------------------------------
修改内容：
- 创建指向/opt/src/delvpnuser.sh的符号链接

22. /opt/src/libreswan-$SWAN_VER/Makefile.inc.local - Libreswan编译配置
--------------------------------------------------------------------
修改内容：
- 创建新文件
- 添加编译选项：
  * WERROR_CFLAGS=-w -s
  * USE_DNSSEC=false
  * USE_DH2=true
  * USE_NSS_KDF=false
  * USE_LINUX_AUDIT=false
  * USE_SECCOMP=false
  * FINALNSSDIR=/etc/ipsec.d
  * NSSDIR=/etc/ipsec.d
- 如果需要，添加：USE_XFRM_INTERFACE_IFLA_HEADER=true

备份文件说明：
=============
所有被修改的配置文件在修改前都会创建备份：
- 备份文件名格式：原文件名.old-YYYY-MM-DD_HH_MM_SS
- 备份包括：
  * /etc/ipsec.conf.old-$SYS_DT
  * /etc/ipsec.secrets.old-$SYS_DT
  * /etc/xl2tpd/xl2tpd.conf.old-$SYS_DT
  * /etc/ppp/options.xl2tpd.old-$SYS_DT
  * /etc/ppp/chap-secrets.old-$SYS_DT
  * /etc/ipsec.d/passwd.old-$SYS_DT
  * /etc/sysctl.conf.old-$SYS_DT
  * /etc/sysconfig/iptables.old-$SYS_DT 或 /etc/sysconfig/nftables.conf.old-$SYS_DT
  * /etc/rc.local.old-$SYS_DT
  * /etc/dhcp/dhclient.conf.old-$SYS_DT

文件权限设置：
=============
- /etc/ipsec.secrets* - 600 (仅root可读写)
- /etc/ppp/chap-secrets* - 600 (仅root可读写)
- /etc/ipsec.d/passwd* - 600 (仅root可读写)
- /etc/rc.local - 755 (可执行)
- /opt/src/*.sh - 755 (可执行)

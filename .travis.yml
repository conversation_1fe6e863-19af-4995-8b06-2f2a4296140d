language: c

jobs:
  include:
    - os: linux
      dist: xenial
      compiler: gcc
    - os: linux
      dist: xenial
      compiler: clang
    - os: linux
      dist: bionic
      compiler: gcc
    - os: linux
      dist: bionic
      compiler: clang
    - os: linux
      dist: focal
      compiler: gcc
    - os: linux
      dist: focal
      compiler: clang

sudo: false

addons:
  apt:
    packages:
      - make
      - gcc-multilib
      - libpcap0.8
      - libpcap0.8-dev

cache:
  directories:
    - $HOME/.ccache

before_script:
  - make clean

script:
  - make
  - ./xl2tpd-control --version
  - ./xl2tpd-control --help

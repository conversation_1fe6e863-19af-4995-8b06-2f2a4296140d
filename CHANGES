v1.3.17 (Jan 20, 2022)
* debian: Add systemd .service [<PERSON>]
* Update TODO file to specify branch work that needs to be completed [<PERSON><PERSON>]
* Correct a typo in the man page xl2tpd.conf(5) [<PERSON><PERSON>]
* xl2tpd-control: fix typo in usage [<PERSON><PERSON><PERSON>]
* Updating debian changelog description [<PERSON><PERSON>]
* control: Fix branch of Vcs-Git [<PERSON>]
* upload [<PERSON>]
* Drop unused lintian warning that produces a lintian error [<PERSON>]
* Update xl2tpd.doc to use README.md [<PERSON><PERSON>]
* Drop patch that cannot be applied any more [<PERSON>]
* Bump debhelper from 10 to 12 [<PERSON>]
* control: Bump Standards-Version to 4.5.0 [<PERSON>]
* Really move changes to patch series [<PERSON>]
* Move c0cce208e4a5 to patch series [<PERSON>]
* upload [<PERSON>]
* Close bug [<PERSON>]
* Use https URL. [<PERSON>]

v1.3.16 (October 23, 2020)
* Re-add braces for if-else that have only statement [<PERSON><PERSON>]
* xl2tpd-control refactoring [<PERSON>]
* fix travis 'script' syntax [<PERSON>]
* adding xl2tpd-control tests to travis [<PERSON> Na<PERSON>ov]
* Re-adding text giving more inofrmation about using ipsec with xl2tpd [<PERSON>r <PERSON>]
* Update README: typo, links to RFC, link to travis [<PERSON> Naumov]
* <PERSON> will test different compiler on linux distro [<PERSON>r <PERSON>]
* Update travis for proper matrix [<PERSON>r <PERSON>]
* Add Focal to travis testing [<PERSON>r <PERSON>]
* yet another man-page update [Alexander Naumov]
* update man-pages, fix typo [Alexander Naumov]
* set_flow: result of operation is garbage or undefined [Alexander Naumov]
* Update README [Alexander Naumov]
* adding xl2tpd.init.patch [Alexander Naumov]
* adding Makefile.patch [Alexander Naumov]
* moving changes to separate file [Alexander Naumov]
* sync/update spec file with official SUSE version [Alexander Naumov]
* Set IP_PKTINFO even if setting of IPPROTO_IP fails (as it was in 1.3.9) [shadyhh]
* Update .gitignore vim swap file [Samir Hussain]
* Travis will test supported Ubuntu LTS distros [Samir Hussain]
* Updating COMPATABILITY_ISSUES with info on Miktrotik servers [Samir Hussain]
* Add work around for Android 10 maxium retries in COMPATIBILITY_ISSUES [Samir Hussain]
* Add compatability issues with Ciso ASA [Samir Hussain]

v1.3.15 (October 13, 2019)
* Fix spacing of CONTRIBUTION.md [Samir Hussain]
* Add CONTRIBUTION.md [Samir Hussain]
* Specify missing log arguments [Patch by github user: 川島和津実]
* Use matrix for .travis.yaml to test for multiple Linux distro [Samir Hussain]
* Fixing .travis.yaml spacing warning [Samir Hussain]
* Sockopt bug fix for multiple IP's [JDTX]
* Add Clang as compiler test for travis [Samir Hussain]
* Add info on building and installing xl2tpd [Samir Hussain]

v1.3.14 (April 17, 2019)
* osport.h: replace SUSv3-specific functions by POSIX variants [Fabrice Fontaine]
* avp: Error Code field in Result Code AVP is optional [Pau Espin Pedrol]
* network_thread: Early continue in loop to remove huge indented block [Pau Espin Pedrol]
*  network_thread: Simplify while loop using for loop [Pau Espin Pedrol]
* network: connect_pppol2tp: early return to avoid huge indentation block [Pau Espin Pedrol]
* xl2tpd: start_pppd: Fix truncation of last character [Pau Espin Pedrol]
* handle_packet: Remove unneded else clause when handling payload [Pau Espin Pedrol]
* control: Split control message handling into its own function [Pau Espin Pedrol]
* handle_packet: Rearrange code flow to simplify it [Pau Espin Pedrol]
* avp: Early failure if no handler to remove indent block [Pau Espin Pedrol]
* xl2tpd: Mark internal symbols as static [Pau Espin Pedrol]
* Fix indentation and whitespace in code block [Pau Espin Pedrol]
* xl2tpd: Remove unused variable [Pau Espin Pedrol]
* network: Add missing close(kernel_fd) on init network failure [Pau Espin Pedrol]
* network: Add missing close(server_fd) on init network failure [Pau Espin Pedrol]
* Add 'cap backoff' option, limiting exponential backoff retries will
  be delayed by exponentially longer time, unless that time is capped
  by configuration. [Bart Trojanowski]
* Add program to show status icon in system tray. [Github user: username34]
* Add info on building and installing xl2tpd [Samir Hussain]
* Update formatting of README.md [Samir Hussain]
* Rename README.xl2tpd to README.md [Samir Hussain]
* Update Debian changelog [Samir Hussain]

v1.3.13 (December 3, 2018)
* Specify email address for reporting security vulnerabilities [Samir Hussain]
* Fix compile warning with USE_KERNEL in xl2tpd.c [Samir Hussain]
* Applying patch that reduces compile warnings and fixes warnings from gcc and clang. [Gareth Ansell]
* Fix compiler warnings in network.c [Gareth Ansell]
* Add a make command for packaging's prep work [Samir Hussain]
* Add Makefile directive for getting version [Samir Hussain]
* Add a preproc for Watchguard firewall (Github issue #136) [daniel1111]
* Convert from ISO-8859 to UTF-8 [Simon Deziel]
* Update README to provide latest info on xl2tpd + Linux kernel 4.15+ [Samir Hussain]
* Use dh_auto_build in order to allow cross compiles [Helmut Grohne]

v1.3.12 (May 18, 2018)
* TOS value to copy to the tunnel header (Yurkovskyy)
* Fix for ENODEV (No such device) error with Linux kernel 4.15 (Douglas Kosovic)
* Update xl2tpd.init (bogdik)
* fix version number and upload (Samuel Thibault)
* import wheezy changes  (Samuel Thibault)

v1.3.11 (March 7, 2018)
* Build packages for Xenial by default (Simon Deziel)
* Bump d/compat to 9 (Simon Deziel)
* Drop d/repack.sh script and refresh d/watch (Simon Deziel)
* Refresh d/control by partly sync'ing from Debian (Simon Deziel)
* Use HTTPS URL in d/copyright (Simon Deziel)

v1.3.10.1 (November 16, 2017)
* Have max retries as a configuration [Samir Hussain]
* Add more into to "select timeout" debug message [Samir Hussain]

v1.3.10 (August 2, 2017)
* Update STRLEN in file.h to 100 (from 80) [Samir Hussain]
* xl2tpd-control: fix xl2tpd hanged up in "fopen" [wendy2001011]
* Update version in spec and opewnrt Makefile. [Samir Hussain]

v1.3.9 (February 8, 2017)
* Add xl2tpd-control man pages (Samir Hussain)
* Update spec file with newest Soure0 and version (Samir Hussain)
* Update License file (Samir Hussain)
* Display PID for call in the logs (Samir Hussain)
* Use left shift rather than pow() function. (Samir Hussain)
* Enable Travis integration (Samir Hussain)
* Remove unnecessary casting of malloc() results (Andrew Clayton)
* Remove an unused line of code in init_config() (Andrew Clayton)
* Fix some undefined behaviour in read_result() (Andrew Clayton)
* Fix feature test macro deprecation warnings (Andrew Clayton)

v1.3.8 (August 11, 2016)
* Another one fix for control buf handling in udp_xmit (Sergey Ryazanov)
* Fixing minor bug in Linux that was introduced by 90368 (Samir Hussain)
* Fix control buffer handling in udp_xmit (rsa9000)
* Avoid using IP_PKTINFO with non-Linux systems (Sergey Ryazanov)
* Remove duplicated UDP checksum disabling (Sergey Ryazanov)
* Handle LDLIBS carefully (Sergey Ryazanov)
* Avoid false-positive warning message from not smart compilers (Sergey Ryazanov)
* Correctly activate XPG4v2 support (Sergey Ryazanov)
* Simplify signal header inclusion (Sergey Ryazanov)
* Adding info on the mailing lists (Samir Hussain)
* Fixing minor spelling typo in code. (Samir Hussain)
* Fixing minor spelling mistakes in xl2tpd.conf.5 and l2tpd.conf.sample (Samir Hussain)
* Removing -fno-builtin from CFLAGS (Samir Hussain)

v1.3.7 (March 29, 2016)
* Adding defensive code to deal with error when pppd exits (Samir Hussain)
* Minor compilation fixes (Yousong Zhou)
* Refresh debian/ from Debian. Thanks! (Simon Deziel)
* Update URL (Simon Deziel)
* Update copyright year (Simon Deziel)
* Add local ip range option. (Patch by by Peter W Morreale)
* Drop RFC 2661 copy. (Simon Deziel)
* debian/control drop legacy Replaces (Simon Deziel)
* Typo fix (Simon Deziel)
* Fix #98 by checking if a valid PID is being killed (Pieter Jordaan)
* Avoid problems with bad avp lengths and remaining hidlen from previous
  iteration (Cristi Cimpianu)
* Fix minor grammar issues in xl2tpd.conf(5) (kballou)
* Fix possible NULL reference when removing lac (Yousong Zhou)
* Describe autodial option in xl2tpd.conf manpage (Anton Leontiev)
* Update URL in BUGS file (Anton Leontiev)
* Add size optimization (Cristi Cimpianu)
* Remove useless returns from magic_lac_tunnel (Cristi Cimpianu)
* Remove duplicate xmit for ZLBs (Cristi Cimpianu)
* Fix segfault on lac remove (Cristi Cimpianu)
* Fix paths in man pages (Taiki Sugawara)
* Stop sending ZLB in response to out of order ZLB from check_control (Cristi Cimpianu)
* Add exponential backoff retransmits (Pieter Willem Jordaan)
* Fix build errors caused by inline function with gcc 5 (Kai Kang)
* Fix memory leaks and accessing free'd memory (Yousong Zhou)
* Fix double-free on dial_no_tmp; (Yousong Zhou)
* Change handle_special to return a value indicating if it frees the buffer (Cristi Cimpianu)
* Remove unnecessary NULL check on lac. (Yousong Zhou)
* xl2tpd-control: show all available commands in --help. (Yousong Zhou)
* Ignore SIGPIPE signal. (Yousong Zhou)
* Unlink result file to prevent leftover a regular file. (Yousong Zhou)
* Introduce new option -l for using syslog as the logging facility. (Yousong Zhou)
* start_pppd: place opts after "plugin pppol2tp.so". (Yousong Zhou)
* Fix typo in reporting available lns count. (Yousong Zhou)
* xl2tpd-control: enhance output of print_error(). (Yousong Zhou)
* xl2tpd-control: cleaup result file atexit(). (Yousong Zhou)
* xl2tpd-control: open control file with O_NONBLOCK. (Yousong Zhou)
* xl2tpd-control: define _GNU_SOURCE to use fmemopen() and friends. (Yousong Zhou)
* xl2tpd-control: check end-of-file when reading pipe to avoid dead loop. (Yousong Zhou)
* Correct CDN message result range (Constantin Calotescu)
* place the PPP frame buffer to the call structure (rsa9000)
* Place the pty read buffer to the call structure (rsa9000)
* Pass pointer to call structure to read_packet() (rsa9000)
* Remove convert arg of read_packet() function (rsa9000)
* Remove dead code (rsa9000)
* Fix the list of ignored files (rsa9000)
* Add checks before closing sockets (Cristi Cimpianu)
* Add a bit more info about existing tunnels and calls (Cristi Cimpianu)
* Fix endless loop (Cristi Cimpianu)
* Add fix for socket leak to fork children (Cristi Cimpianu)
* Random fixes (Constantin Calotescu)
* Solve some memory leaks that show up after several days of running with
  flapping tunnels and calls. (Cristi Cimpianu)
* Fix for avoiding xltpd occasionally going into an endless loop. (Damian Ivereigh)
* Fixed issue with strtok modifying contents when pushing details for ppd plugins (Michael Lawson)
* Added the ability to add a pppd plugin and params to an lns (Michael Lawson)
* Modified lns_remove to close each call rather than just calling destroy_tunnel() (Michael Lawson)
* Added control method to remove an lns (Michael Lawson)
* Refactored the do_control() method to use a handler approach for processing (Michael Lawson)
* Fixed potential null pointer when creating a new lns (Michael Lawson)
* Added status control command for lns, this returns tunnel and call information via the control socket (Michael Lawson)
* Added control support for adding lns and status command in xl2tp-control (Michael Lawson)
* Added control pipe method CONTROL_PIPE_REQ_LNS_ADD_MODIFY to modify LNS configuration (Michael Lawson)
* Introduced shared control request types (Michael Lawson)
* Fixed typo in xl2tpd.conf.5 (paina)
* Some malloc/free sanity patches. (Patrick Naubert)
* Better NETBSD support. (Patrick Naubert)
* Prevent a DEBUG message from being sent to syslog when not debugging. (Patrick Naubert)

v1.3.6 (Jan 15, 2014)
* I keep screwing up the version number.  Changes to CHANGES and l2tp.h
* Fix the size of the lenght param for AVP headers. This should fix Android
  support no matter how the compiler optimizes.

v1.3.5 (Jan 15, 2014)
* Re-define the add_header() function as the compiler was screwing up the lenght|MBIT logic.
  THIS RE-ENABLES ANDROID SUPPORT which had been broken since 1.2.7

v1.3.4 (Jan 13, 2014)
* Revert "Patches from Fedora to use Openssl MD5 instead of our own"
    This patch forced us to ask for a license exception, which we cannot do.
* Revert "Add a license exception to link xl2tpd with OpenSSL"
    Without the OpenSSL MD5 requirement, we no longer need to ask for a license exception.

v1.3.3 (Jan 3, 2014)
* License exception for linking with OpenSSL
   (this is because we do not use our MD5 anymore)
* Check write return code on control socket (fixes FTBFS with -Wall) [Simon Deziel]

v1.3.2 (Nov 15, 2013)
* Remove unused variables reported by gcc -Wunused [Paul Wouters]
* Retain password when redial is enabled [Ted Phelps]
* Respect LDFLAGS, original author unknown [Jeremy Olexa]
* Turn off UDP checksums [mcr]
* Respect CFLAGS for xl2tpd-control [Mike Gilbert]
* Patches from Fedora to use Openssl MD5 instead of our own [Patrick Naubert]
* Cosmetic fix log warning about IPsec SAref kernel mod support [Pavel Kopchyk]
* Upgrade options (in global contex) according to their keyword words in 'file.c' [Pavel Kopchyk]
* Actually force userspace when using SAref [Sergey Fionov]
* Enable kernel-mode by default [Sergey Fionov]
* Fix kernel support for 2.6.23+ [Sergey Fionov]
* Remove if_pppol2tp.h header [Sergey Fionov]
* Tell pppd to set LNS mode flag on kernel pppol2tp session socket. [Sergey Fionov]
* Wrap connect_pppol2tp() with ifdef [Sergey Fionov]
* Check for existence of SO_NO_CHECK before using it [Stuart Henderson]
* Check that argv[1] isn't null before strncmp() [Stuart Henderson]
* Set a valid msg_controllen, OpenBSD needs it [Stuart Henderson]
* Avoid type punning: it makes gcc grumpy. [Ted Phelps]
* Cope with comment lines that extend beyond 120 characters. BZ#806963. [Ted Phelps]
* Document the default duration of the redial timeout option. [Ted Phelps]
* Don't call grantpt(). [Ted Phelps]
* Fix an uninitialize memory access. [Ted Phelps]
* Fix udp_xmit to work on Linux *and* OpenBSD [Ted Phelps]
* Increase the size of the buffer used to read config file lines. [Ted Phelps]
* Make it possible to prevent xl2tpd from overwriting ppp's ipparam [Ted Phelps]
* Quash more valgrind warnings. [Ted Phelps]
* Set msgh.msg_controllen before calling CMSG_FIRSTHDR [Ted Phelps]
* + use address from the last received UDP packet in UDP messages response [Tomas Chmelar]
* Clean up samples [Tuomo]
* Don't mark some AVPs as mandatory [wangxiaoguang]
  http://bugs.debian.org/cgi-bin/bugreport.cgi?bug=680146
* Remove a 'sleep' in control_finish [wangxiaoguang]

v1.3.1 (Oct 6, 2011)
* Suse updated to spec and init files [Shinichi Furuso]
* SAREF: Changed IP_IPSEC_REFINFO to global option "saref refinfo" [Paul]
* Updated configuration examples with SAref [Paul]
* samples: cleaned up samples and removed broken ones [Tuomo]

v1.3.0 (July 23, 2011)
* Added xl2tpd-control [Alexander Dorokhov]
* Added 'a' (add) and 'd' (delete) control options [Alexander Dorokhov]
* Refresh debian/ from Debian. [Roberto C. Snchez]
* Buffer overrun in reading >16 char l2tp-secrets [Matt Domsch]
  (https://bugzilla.redhat.com/show_bug.cgi?id=689178)
* xl2tpd may leaks file descriptors [Steve Barth]
* xl2tpd: field o_pad in "struct payload_hdr" unnecessary. RFC 2661 [Ilya]
* Fix logging in write_packet() [Ilya]
* Bug tracker bugs fixed:
  #1119 Segfault upon config error [Andrey Cherny]
  #1223 Gentoo QA warning: dereferencing pointer [Andrey Cherny]
  #1236 xl2tpd hungs and wont redial after communication fail [Andrey Cherny]
  #1237 delayed null pointer check [Andrey Cherny]

v1.2.8
* Makefile: fix compilation with --as-needed linker flag [Vladimir V. Kamarzin]
* Workaround for apple clients missing htons() [Brian Mastenbrook]
* Log destination ip and port in case of send failure [Mika Ilmaranta]
* Added Default-Stop: to fedora initscript [Paul]
* Bug tracker bugs fixed:
  #1078 xl2tpd doesn't pass 'ipparam' to pppd and pppd won't get
        client ip (Xiaoguang WANG)

v1.2.7
* Reduce time in signal handlers where we cannot log [Shinichi Furuso]
* Add rx/tx bps speed setting options [Tony Hoyle]
  (http://bugs.debian.org/cgi-bin/bugreport.cgi?bug=578070)
* Rename FFLAGS to IPFLAGS to avoid clashing on debian [Paul]
* spelling fix (dont -> don't) [Paul]

v1.2.6
* Partial fix for compiling on OpenBSD [synapse]
* add missing setting of logopen=1 in init_log() [Shingo Yamawaki]
* xltpd could deadlock on syslog() call in signal handler [Bart Trojanowski]
* fix fedora/centos spec file [Paul]

v1.2.5
* Fix initscript for https://bugzilla.redhat.com/show_bug.cgi?id=247100
* Fix for two Windows machines behind the same NAT with the samed
  number of l2tp connection attempts since boot [Shinichi Furuso]

v1.2.4
* Fixes to Suse spec file [Shingo Yamawaki]
* unclutter logs for 'select timeout' [Shingo Yamawaki]
* Make sure child_handler and destroy_call won't conflict when pppd is killed
  [Mika Ilmaranta/ Tuomo]
* Workaround for broken kernels that send duplicate pids to waitpid()
  See: https://bugzilla.redhat.com/show_bug.cgi?id=486945 [Mika / Tuomo]
* Fix pppd option from legacy -detach to nodetach [Tuomo]

v1.2.3
* Fixes for prefix/destdir and spec files [paul/tuomo]
* Use pcap not pcap-devel on suse, rhel and centos [paul/tuomo/shingo]
* Added pfc to contrib. pfc is a tool to compile active-filters
  for pppd, which can be used for dial-on-demand filters [paul/roberto]
* Bug tracker bugs fixed
  # 998: xl2tpd-1.2.2 Makefile sets wrong path for mandir

v1.2.2
* PPP CHAP authentication using plugin passwordfd.so failed [<EMAIL>]
* Use SIGALRM only in select(). This prevents a problem where a pppd child
  (eg ntpd via ppp-up.d/ script) is using signaling too. [Shingo Yamawaki]
* A file descriptor is left opened when exec'ing pppd. [Shingo Yamawaki]
* When select() is interrupted, readfds should not be used. [Shingo Yamawaki]
* Modifications to Makefile to support DESTDIR [paul]
* Modifications to compile on OpenBSD [Stephen Ayotte]
* Bug tracker bugs fixed
  #955: refuse authentication is backward for LAC sections [Dean Scarff]

v1.2.1
* Fixes to Suse init file and spec file [paul]
* Changed some build defaults in Makefile [paul]

v1.2.0
* Synchronised IP_IPSEC_REFINFO define with KLIPSNG patch [paul]
* Fixed versioning and bumped to 1.2.0 [paul]

v1.1.12
* Fix for dropped packets and wrong disconnects. [Ray Overland / Tuomo]
* Included debian directory from Roberto C. Sanchez <<EMAIL>>

v1.1.11
* Support for passwordfd when using xl2tpd as client.
  Patch by David MacKinnon <<EMAIL>>
* Add DEBUG_AUTH comments to the Makefile [paul]
* Workaround for Cisco routers that do not send transmit speed or framing
  type [paul]
* Fix two old l2tpd references to xl2tpd (syslog used wrong name) [paul]

v1.1.10
* add pid to pppd logging [tuomo]
* don't specify compiler flags (overrides packaging flags in rpm) [tuomo]
* minor documentation fixes [tuomo/paul]

v1.1.09
* Forgot to bump version number, so to avoid confusing, I bumped everything
  to 1.1.09

v1.1.08
* Confirmed pppd bug of not always terminating on SIGTERM. The new define
  TRUST_PPPD_TO_DIE determins whether we send SIGTERM or SIGKILL, with
  SIGKILL being the (new) default. (ppp-2.4.2-6.4.RHEL4 is known to be
  broken)

v1.1.07
* Fix for unaligned header field accesses crashes on RISC by Dave S. Miller
  (# 735)
* Added and enabled pppd debugging code to assist locating a serious
  xl2tpd infinite loop when pppd does not die after a SIGTERM.
* Complete support for pppol2tp's kernel mode L2TP. Patch by Cedric
* Make spec file Fedora Extras compliant
* Added pppol2tp-linux-2.4.27.patch to contrib/
* Pidfile fixes (by Tuomo)
* Fix creation of pid file if /var/run/xl2tpd does not exist.
* Fix compile without SANITY defined (Charlie Brady <<EMAIL>>)
* Fix configuration filename for the ppp options file (#725 by Tuomo)
* Fixes to compile with all DEBUG_* statements enabled
* Documented all DEBUG_* statements in Makefile

v1.1.06
* Build xl2tpd and use /etc/xl2tpd/xl2tpd.* configuration files with fallback
  to /etc/l2tpd/l2tpd.* configuration files.
* Support for pppol2tp's kernel mode L2TP.
  Patch by Cedric Schieli <<EMAIL>>
* Documented IPsec SA reference tracking for use with Openswan
* Added patents documentation.
* Migration support on xl2tpd.spec for l2tpd -> xl2tpd

v1.1.05
* Changed versioning scheme to match Xelerance standards
* IPsec SA reference tracking added (used with Openswan's IPsec transport mode)
  This adds support for multiple clients behind the same NAT router, and
  multiple clients on the same internal IP behind different NAT routers.
* Fix for Windows clients that send the wrong tunnel ID for closing tunnels

v1.04
* actually, 1.03 tag in GIT was in the wrong place. This is the right release.

v1.03
* fixes for gcc 4.xx compilation

v1.02
* udpated CHNANGELOG

v1.01
* various debugging added, but debugging should not be on by default
* async/sync conversion routines must be ready for possibility that the read
  will block due to routing loops
* refactored control socket handling.
* use man page in doc/
* move all logic about pty usage to pty.c try ptmx first. if it fails try
  legacy ptys
* rename log() to l2tp_log(), as "log" is a math function.

v1.00
* First version managed by Xelerance, called xl2tpd.
* If we aren't deamonized, then log to stderr.
* added install: and DESTDIR support

0.70
--
Change path for config files from /etc/l2tp to /etc/l2tpd (<EMAIL>)
Turn of echo no ptys to pppd (Damien de Soto)
Add pty name to command line passed to pppd (Chris Wilson)
Added listen-addr parameter to l2tpd.conf (<EMAIL>)
Close stdin when in daemon mode (<EMAIL>)
Improve interoperability with MSL2TP (<EMAIL>)
Eliminate some warnings (<EMAIL>)

0.69
--
Edited l2tpd.conf.5 man page to correct some information
Added l2tpd.8 and l2tp-secrets.5 man pages
Zero'ed out memory malloced to hold challenge, otherwise we may pass
    wrong challenge to md5 code

0.68
--
Updated copyright notice on all relevent files
Changed vendor name as it appears in AVP's
Add new sources of randomness, reading /dev/urandom
Seed rand() with time()
Stubs available for egd randomness source, not implemented yet though
Don't close fd 0 as workaround for signal problems in daemon mode
Fix some off by 6 errors in avp handling

0.67
--
close pty connecting to pppd in child_handler()
Add code to daemonize correctly
Add command line options
    -D to not daemonize
    -p to specify a pidfile
    -c to specify a config file
    -s to specify a secrets file
Catch a SIGHUP that's coming from who-knows-where and do nothing

0.66
--
Fixed tunnel authentication mechanism so that it works!
Fixed several segfaults...some in debugging code

0.65.1
--
Reformatted all .c and .h files using GNU indent

0.65
--
Fix to handling SLI packets
reformatted some code in a few small places
Added valid, new (since L2TP draft days) result codes
autodialed calls switched to be "Incoming calls" rather than "Outgoing"
Re-arranged some header declarations
Remote systems may use the same Tunnel ID...this is OK
Look for l2tpd.conf in /etc/l2tp and in /etc/l2tpd...look for
    l2tp-secrets int he same directory
Portability enhancement (act.sa_restorer only used on i386?)
    (Jean-Francois Dive)

0.64
--
Too many that I lost track...
Scaleability improvements from Huiban Yoann at Siemens
Rudimentary Outgoing Call Request system
As in CREDITS, "an uncountable amount of little bug fixes"

0.63
--
Syslog support added!!!
Improved data sequencing & flow control serial number checking
Removed call flow/session control serial number checking in ICRQ
  -- Did we do this already and we're going mindless? :D
Removed checking of now-defunct R bit
Changed PPP framing to always sync
Various and asundry other fixes

NOW OPERABLE WITH CISCO IOS 12.1
Continued interoperability improvements with Windows 2000 clients

0.62
--
Removed call flow/session control (inapplicable as of RFC spec draft 13)
Corrected invalid Receive Window Size AVP in ICCN
Corrected Bearer Capabilities non-requirement in SCCRQ & SCCRP
Verified operability with Cisco 3000 series

0.61
--
Fixed shutdown of PPPd from SIGKILL to SIGTERM
Beginning code cleanup and interoperability testing

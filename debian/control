Source: xl2tpd
Section: net
Priority: optional
Maintainer: <PERSON><PERSON> <<EMAIL>>
Uploaders: <PERSON> <<EMAIL>>
Homepage: https://www.xelerance.com/software/xl2tpd/
Vcs-Browser: https://github.com/xelerance/xl2tpd
Vcs-Git: https://github.com/xelerance/xl2tpd.git -b debian-sid
Build-Depends: debhelper-compat (= 12), libpcap0.8-dev
Standards-Version: 4.6.0

Package: xl2tpd
Architecture: any
Multi-Arch: foreign
Provides: l2tpd
Depends: ${shlibs:Depends}, ${misc:Depends}, ppp, lsb-base (>= 3.0-6)
Description: layer 2 tunneling protocol implementation
 xl2tpd is an open source implementation of the L2TP tunneling
 protocol (RFC2661).  xl2tpd is forked from l2tpd and is maintained by
 Xelerance Corporation.
 .
 The main purpose of this protocol is to tunnel PPP frames through IP
 networks.  It implements both LAC and LNS role in the L2TP networking
 architecture.


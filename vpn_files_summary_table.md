# VPN设置脚本文件修改摘要表

## 主要配置文件修改

| 文件路径 | 修改方式 | 主要内容 | 关键配置 |
|---------|---------|---------|---------|
| `/etc/ipsec.conf` | 完全重写 | IPsec连接配置 | 加密算法、网络段、DPD设置 |
| `/etc/ipsec.secrets` | 完全重写 | PSK密钥 | `%any %any : PSK "$VPN_IPSEC_PSK"` |
| `/etc/xl2tpd/xl2tpd.conf` | 完全重写 | L2TP服务配置 | IP池、本地IP、认证设置 |
| `/etc/ppp/options.xl2tpd` | 完全重写 | PPP连接选项 | MTU、DNS、认证方式 |
| `/etc/ppp/chap-secrets` | 完全重写 | L2TP用户认证 | 用户名密码对 |
| `/etc/ipsec.d/passwd` | 完全重写 | XAUTH用户认证 | 加密密码存储 |

## 系统配置文件修改

| 文件路径 | 修改方式 | 主要内容 | 关键配置 |
|---------|---------|---------|---------|
| `/etc/sysctl.conf` | 追加内容 | 内核网络参数 | IP转发、TCP优化、BBR |
| `/etc/sysconfig/iptables` | 完全重写 | 防火墙规则 | VPN端口、NAT转发 |
| `/etc/sysconfig/nftables.conf` | 完全重写 | NFTables规则 | 现代防火墙配置 |
| `/etc/fail2ban/jail.local` | 创建新文件 | SSH保护 | 暴力破解防护 |
| `/etc/rc.local` | 追加内容 | 开机启动 | VPN服务自启动 |
| `/etc/firewalld/firewalld.conf` | 条件修改 | Firewalld设置 | NFTables所有权 |
| `/etc/dhcp/dhclient.conf` | 条件修改 | DHCP配置 | GCP MTU修复 |
| `/etc/crypto-policies/back-ends/nss.config` | 条件修改 | 加密策略 | SHA1支持 |

## 辅助脚本和目录

| 路径 | 修改方式 | 内容来源 | 用途 |
|------|---------|---------|------|
| `/opt/src/` | 创建目录 | 脚本创建 | 存放源码和脚本 |
| `/opt/src/ikev2.sh` | 下载重命名 | GitHub下载 | IKEv2管理 |
| `/opt/src/addvpnuser.sh` | 下载重命名 | GitHub下载 | 添加VPN用户 |
| `/opt/src/delvpnuser.sh` | 下载重命名 | GitHub下载 | 删除VPN用户 |
| `/usr/bin/ikev2.sh` | 创建链接 | 符号链接 | 全局访问IKEv2管理 |
| `/usr/bin/addvpnuser.sh` | 创建链接 | 符号链接 | 全局访问用户管理 |
| `/usr/bin/delvpnuser.sh` | 创建链接 | 符号链接 | 全局访问用户管理 |
| `/opt/src/libreswan-*/Makefile.inc.local` | 创建新文件 | 编译配置 | Libreswan编译选项 |

## 关键配置内容详解

### IPsec配置 (`/etc/ipsec.conf`)
```
- 加密算法：AES256/128-SHA2/SHA1
- 网络配置：L2TP网段、XAUTH网段
- 连接参数：24小时生命周期、DPD检测
- 协议支持：IKEv1、L2TP、XAUTH
```

### 网络参数 (`/etc/sysctl.conf`)
```
- IP转发：net.ipv4.ip_forward = 1
- 重定向：禁用send_redirects和accept_redirects
- TCP优化：16MB缓冲区
- BBR拥塞控制：如果内核支持
```

### 防火墙规则 (iptables/nftables)
```
- 开放端口：UDP 500, 4500, 1701
- NAT转发：L2TP和XAUTH网段
- 安全策略：仅允许IPsec保护的L2TP
```

### 用户认证配置
```
- L2TP认证：CHAP-secrets文件
- XAUTH认证：passwd文件（MD5加密）
- 默认用户：vpnuser（如未指定）
```

## 备份策略

| 原文件 | 备份文件格式 | 示例 |
|--------|-------------|------|
| 所有被修改的配置文件 | `原文件.old-时间戳` | `/etc/ipsec.conf.old-2025-01-15_10_30_45` |

## 文件权限设置

| 文件类型 | 权限 | 说明 |
|---------|------|------|
| 密钥文件 | 600 | 仅root可读写 |
| 配置文件 | 644 | root写，其他只读 |
| 脚本文件 | 755 | 可执行权限 |
| 启动脚本 | 755 | 可执行权限 |

## 服务配置变更

| 服务 | 操作 | 说明 |
|------|------|------|
| firewalld | 禁用并屏蔽 | 避免与iptables/nftables冲突 |
| ipsec | 启用并启动 | IPsec VPN服务 |
| xl2tpd | 启用并启动 | L2TP隧道服务 |
| fail2ban | 启用并启动 | SSH保护服务 |
| iptables/nftables | 启用 | 防火墙服务 |

## 网络配置默认值

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| L2TP网段 | ************/24 | L2TP客户端网络 |
| L2TP本地IP | ************ | 服务器在L2TP网段的IP |
| L2TP IP池 | ************0-250 | 分配给L2TP客户端的IP范围 |
| XAUTH网段 | ************/24 | XAUTH客户端网络 |
| XAUTH IP池 | *************-250 | 分配给XAUTH客户端的IP范围 |
| 主DNS | ******* | Google公共DNS |
| 备用DNS | ******* | Google备用DNS |

## 安全特性

| 特性 | 实现方式 | 说明 |
|------|---------|------|
| 密钥保护 | 文件权限600 | 防止非root用户读取密钥 |
| 防火墙保护 | iptables/nftables规则 | 仅允许必要端口 |
| L2TP保护 | IPsec封装 | L2TP流量必须经过IPsec加密 |
| SSH保护 | fail2ban | 防止暴力破解 |
| 配置备份 | 自动备份 | 修改前自动创建备份 |

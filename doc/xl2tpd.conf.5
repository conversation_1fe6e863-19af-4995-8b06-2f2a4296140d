.TH xl2tpd.conf 5 "Sep 2020"
.SH NAME
xl2tpd.conf \- L2TPD configuration file
.SH DESCRIPTION
The xl2tpd.conf file contains configuration information for xl2tpd,
the free implementation of l2tp protocol.

The configuration file is composed of sections and parameters. Each section
has a given name which will be used when using the configuration FIFO
(normally /var/run/xl2tpd/l2tp\-control). See xl2tpd.8 for more details.

The specific given name 
.B default
will specify parameters applicable for all the following sections.

.SH GLOBAL SECTION
.TP 
.B auth file
Specify where to find the authentication file used to authenticate
l2tp tunnels. The default is /etc/xl2tpd/l2tp\-secrets.

.TP 
.B ipsec saref
Use IPsec Security Association tracking. When this is enabled, packets
received by xl2tpd should have two extra fields (refme and refhim) which
allows tracking of multiple clients using the same internal NATed IP
address, and allows tracking of multiple clients behind the same
NAT router. This needs to be supported by the kernel. Currently, this
only works with Openswan KLIPS in "mast" mode. (see http://www.openswan.org/)

Set this to yes and the system will provide proper SAref values in the
recvmsg() calls.

Values can be 'yes' or 'no'. The default is 'no'.

.TP 
.B saref refinfo
When using IPsec Security Association trackinng, a new setsockopt is used.
Since this is not (yet?) an official Linux kernel option, we got bumped.
Openswan upto 2.6.35 for linux kernels up to 2.6.35 used a saref num of 22.
Linux 3.6.36+ uses 22 for IP_NODEFRAG. We moved our IP_IPSEC_REFINFO to 30.
If not set, the default is to use 30. For older SAref patched kernels, use 22.

.TP 
.B listen-addr
The IP address of the interface on which the daemon listens.  By default,
it listens on INADDR_ANY (0.0.0.0), meaning it listens on all interfaces.

.TP 
.B port
Specify which UDP port xl2tpd should use. The default is 1701.

.TP 
.B access control
If set to 'yes', the xl2tpd process will only accept connections from
peers addresses specified in the following sections. The default is 'no'.

.TP
.B debug avp
Set this to 'yes' to enable syslog output of L2TP AVP debugging information.

.TP
.B debug network
Set this to 'yes' to enable syslog output of network debugging information.

.TP
.B debug packet
Set this to 'yes' to enable printing of L2TP packet debugging information.
Note: Output goes to STDOUT, so use this only in conjunction with the
.B -D
command line option.

.TP
.B debug state
Set this to 'yes' to enable syslog output of FSM debugging information.

.TP
.B debug tunnel
Set this to 'yes' to enable syslog output of tunnel debugging information.

.TP
.B max retries
Specify how many retries before a tunnel is closed. If there is no
tunnel, then stop re-transmitting. The default is 5.

.SH LNS SECTION
.TP 
.B exclusive
If set to 'yes', only one control tunnel will be allowed to be built
between 2 peers.

.TP 
.B (no) ip range
Specify the range of IP addresses the LNS will assign to the connecting
LAC PPP tunnels. Multiple ranges can be defined. Using the 'no'
statement disallows the use of that particular range.  Ranges are defined
using the format IP \- IP (example: ******* \- *******0).  Note that either
at least one
.B ip range
option must be given, or you must set
.B assign ip
to no.

.TP
.B assign ip
Set this to 'no' if xl2tpd should not assign IP addresses out of the pool
defined with the
.B ip range 
option.  This can be useful if you have some other means to assign IP
addresses, e. g. a pppd that supports RADIUS AAA.


.TP 
.B (no) lac
Specify the IP addresses of LAC's which are allowed to connect to xl2tpd
acting as a LNS. The format is the same as the 
.B ip range 
option.

.TP 
.B hidden bit
If set to 'yes', xl2tpd will use the AVP hiding feature of L2TP. To get
more information about hidden AVP's and AVP in general, refer to rfc2661
(add URL?)

.TP 
.B local ip
Use the following IP as xl2tpd's own IP address.

.TP
.B local ip range
Specify the range of addresses the LNS will assign as the local address
to connecting LAC PPP tunnels.  This option is mutually exclusive with
the
.B local ip
option and is useful in cases where it is desirable to have a unique IP
address for each tunnel.  Specify the range value exactly like the
.B ip range
option.  Note that the
.B assign ip
option has no effect on this option.

.TP 
.B length bit
If set to 'yes', the length bit present in the l2tp packet payload
will be used.

.TP 
.B (refuse | require) chap
Will require or refuse the remote peer to get authenticated via CHAP for the 
ppp authentication.

.TP 
.B (refuse | require) pap
Will require or refuse the remote peer to get authenticated via PAP for the 
ppp authentication.

.TP 
.B (refuse | require) authentication
Will require or refuse the remote peer to authenticate itself.

.TP 
.B unix authentication
If set to 'yes', /etc/passwd will be used for remote peer ppp authentication.

.TP 
.B hostname
Will report this as the xl2tpd hostname in negotiation.

.TP 
.B ppp debug
This will enable the debug for pppd.

.TP
.B pass peer
Pass the peer's IP address to pppd as ipparam. Enabled by default.

.TP 
.B pppoptfile
Specify the path for a file which contains pppd configuration parameters
to be used.

.TP 
.B call rws
This option is deprecated and no longer functions. It used to be used
to define the flow control window size for individual L2TP calls or
sessions. The L2TP standard (RFC2661) no longer defines flow control or
window sizes on calls or sessions.

.TP 
.B tunnel rws
This defines the window size of the control channel.  The window size is
defined as the number of outstanding unacknowledged packets, not as a
number of bytes.

.TP 
.B flow bits
If set to 'yes', sequence numbers will be included in the communication.
The feature to use sequence numbers in sessions is currently broken and
does not function.

.TP 
.B challenge
If set to 'yes', use challenge authentication to authenticate peer.

.TP
.B rx bps
If set, the receive bandwidth maximum will be set to this value

.TP
.B tx bps
If set, the transmit bandwidth maximum will be set to this value

.SH "LAC SECTION"
The following are LAC specific configuration flags. Most of those
described in the LNS section may be used in a LAC context, where
it makes common sense (essentially l2tp protocols tuning flags and
authentication / ppp related ones).

.TP 
.B lns
Set the dns name or ip address of the LNS to connect to.

.TP
.B autodial
If set to 'yes', xl2tpd will automatically dial the LAC during startup.

.TP 
.B redial
If set to 'yes', xl2tpd will attempt to redial if the call get
disconnected.  Note that, if enabled, xl2tpd will keep passwords in
memory: a potential security risk.

.TP 
.B redial timeout
Wait X seconds before redial. The redial option must be set to yes
to use this option. Defaults to 30 seconds.

.TP 
.B max redials 
Will give up redial tries after X attempts.

.SH FILES
.IP /etc/xl2tpd/xl2tpd.conf
Configuration file of xl2tpd, used by default.

.IP /etc/xl2tpd/l2tp-secrets
Secrets file, used by default.

.IP /var/run/xl2tpd/l2tp\-control
Control file, used by default.


.SH BUGS
Please use the github project page
https://github.com/xelerance/xl2tpd
to send bugreports, issues and any other feedback


.SH SEE ALSO
xl2tpd(8),
xl2tpd-control(8),
pppd(8)

.SH COPYLEFT
This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program (see the file LICENSE); if not, see
https://www.gnu.org/licenses/, or contact Free Software Foundation, Inc.,
51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.


.SH CONTRIBUTORS
Alexander Dorokhov <<EMAIL>>
.br
Alexander Naumov <<EMAIL>>


.SH AUTHORS
Forked from l2tpd by Xelerance: https://github.com/xelerance/xl2tpd

Michael Richardson <<EMAIL>>
.br
Paul Wouters <<EMAIL>>
.br
Samir Hussain <<EMAIL>>


Previous development was hosted at sourceforge
(http://www.sourceforge.net/projects/l2tpd) by:
.P
Scott Balmos <<EMAIL>>
.br
David Stipp <<EMAIL>>
.br
Jeff McAdams <<EMAIL>>


Based off of l2tpd version 0.61.
Many thanks to Jacco de Leeuw <<EMAIL>> for maintaining l2tpd.
.br
Copyright (C)1998 Adtran, Inc.
.br
Mark Spencer <<EMAIL>>

.TH xl2tpd-control 8  "Sep 2020"

.SH NAME
xl2tpd\-control \- xl2tpd control utility.

.SH DESCRIPTION
xl2tpd is an implementation of the Layer 2 Tunneling Protocol (RFC 2661).
L2TP allows to tunnel PPP over UDP. Some ISPs use L2TP to tunnel user sessions
from dial-in servers (modem banks, ADSL DSLAMs) to back-end PPP servers.
Another important application is Virtual Private Networks where the IPsec
protocol is used to secure the L2TP connection (L2TP/IPsec, RFC 3193).

xl2tpd works by opening a pseudo-tty for communicating with pppd.
It runs completely in userspace but supports kernel mode L2TP.

xl2tpd supports IPsec SA Reference tracking to enable overlapping internak
NAT'ed IP's by different clients (eg all clients connecting from their
linksys internal IP *************) as well as multiple clients behind
the same NAT router.

.SH SYNOPSIS
.HP
\fBxl2tpd-control\fR [\fI-c\fR PATH] \fI<COMMAND>\fR \fI<TUNNEL_NAME>\fR [\fIOPTIONS\fR]


.SH OPTIONS
.TP
.B -c
This option specifies xl2tpd control file.

.TP
.B -d
This option enables debugging mode.

.SH COMMANDS
.TP
.B add-lac
Adds new or modify existing LAC (L2TP Access Concentrator) configuration.
Configuration should be specified as a command options in <key>=<value>
pairs format. See available options in xl2tpd.conf(5).

.TP
.B connect-lac
Establish new connection to LAC.
Username and secret for tunnel can be passed as a command options.

.TP
.B disconnect-lac
Disconnects tunnel.

.TP
.B remove-lac
Removes existing LAC configuration.
xl2tpd disconnects the tunnel before removing.

.TP
.B add-lns
Adds new or modify existing LNS (L2TP Network Server) configuration.

.TP
.B remove-lns
Removes existing LNS configuration.

.TP
.B status-lns
Check the status of LNS.

.TP
.B available
Check availability.

.SH BUGS
Please use the github project page
https://github.com/xelerance/xl2tpd
to send bugreports, issues and any other feedback.

.SH SEE ALSO
xl2tpd.conf(5),
xl2tpd(8),
pppd(8)

.SH COPYLEFT
This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program (see the file LICENSE); if not, see
https://www.gnu.org/licenses/, or contact Free Software Foundation, Inc.,
51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

.SH CONTRIBUTORS
Alexander Dorokhov <<EMAIL>>
.br
Alexander Naumov <<EMAIL>>

.SH AUTHORS
Forked from l2tpd by Xelerance: https://github.com/xelerance/xl2tpd

Michael Richardson <<EMAIL>>
.br
Paul Wouters <<EMAIL>>
.br
Samir Hussain <<EMAIL>>

Previous development was hosted at sourceforge
(http://www.sourceforge.net/projects/l2tpd) by:
.P
Scott Balmos <<EMAIL>>
.br
David Stipp <<EMAIL>>
.br
Jeff McAdams <<EMAIL>>

Based off of l2tpd version 0.61.
Many thanks to Jacco de Leeuw <<EMAIL>> for maintaining l2tpd.

.br
Copyright (C)1998 Adtran, Inc.
.br
Mark Spencer <<EMAIL>>


http://www.ietf.org/ietf/IPR/CISCO-L2TP

The following was received on March 2, 1999 from
<PERSON>  (<EMAIL>)

Cisco has a patent pending that may relate to this proposed standard. If
this proposed standard is adopted by IETF and any patents issue to Cisco or
its subsidiaries with claims that are necessary for practicing this
standard, any party will be able to obtain the right to implement, use and
distribute the technology or works when implementing, using or distributing
technology based upon the specific specification(s) under openly specified,
reasonable, non-discriminatory terms.


Requests may be sent to:

Robert Barr
Suite 280
2882 Sand Hill Road
Menlo Park Ca 94025

Phone: ************


Note: On July 30, 1999, we were informed that the patent office had assigned 
      the number 5,918,019 for the patent

--------------------------
Cisco allows anyone to use their patent as long as it is IETF RFC
compliant. This is Cisco's standard policy on patents for their IETF
work. In fact, their statement was made before being awarded the
patent. They complied fully with the IPR disclosure policy of the
IETF. The IETF does not release RFC's that are limited or in any way
discriminatory in their use. The patent holder (in this case Ciso)
agree to a royalty free, unrevocable use of their patent as needed for
implementing the IETF standards.

If there were any limitations on the implementation and use of L2TP,
the L2TP working group would not exist any more, and no new protocol
additions or changes would be accepted as RFC standard.

The L2TP became an IETF standard, see http://www.ietf.org/rfc/rfc2661.txt

Notice the RFC was issued after the disclosure for IPR by Cisco, so
the IETF fully knew about the patent and confirmed that there were no
restrictions before it issued the RFC.

   --- Paul Wouters <<EMAIL>>


.\"                                      Hey, EMACS: -*- nroff -*-
.\" First parameter, NAME, should be all caps
.\" Second parameter, SECTION, should be 1-8, maybe w/ subsection
.\" other parameters are allowed: see man(7), man(1)
.TH PFC 1 "October 30, 2008"
.\" Please adjust this date whenever revising the manpage.
.\"
.\" Some roff macros, for reference:
.\" .nh        disable hyphenation
.\" .hy        enable hyphenation
.\" .ad l      left justify
.\" .ad b      justify to both left and right margins
.\" .nf        disable filling
.\" .fi        enable filling
.\" .br        insert line break
.\" .sp <n>    insert n+1 empty lines
.\" for manpage-specific macros, see man(7)
.SH NAME
pfc \- active precompiled filters generator
.SH SYNOPSIS
.B pfc
.RI <expression>
>/etc/ppp/your.active.filter
.SH DESCRIPTION
This manual page documents briefly the
.B pfc
command.
.PP
.\" TeX users may be more comfortable with the \fB<whatever>\fP and
.\" \fI<whatever>\fP escape sequences to invode bold face and italics, 
.\" respectively.
\fBpfc\fP is the Precompiled Filter Compiler - a tool to generate "active precompiled filters".  If your pppd supports this feature, you can use this utility to generate the filter files.  The Active Filter allows a connect on demand pppd to determine what is 'interesting' traffic, and then initiate the PPP session.  The tool allows you to create the filters, in libpcap format, for use by pppd.  Common filters are used to ignore traffic
(ie: ntp, various protocol keepalives, etc...) so PPP sessions are not initiated until 'real' traffic requires them.
.PP
Note that the generated compiled filter expression is specific to point-to-point
links, and differs from the format generated by tcpdump -ddd.
.PP
(specify precompiled-active-filter=/etc/ppp/your.active.filter in the ppp options file)
.SH EXAMPLE
/usr/bin/pfc ntp and ldap > /etc/ppp/your.active.filter
.SH SEE ALSO
pfc is from the FLoppy Isdn 4 Linux project - see http://www.fli4l.de/en/home/<USER>/
.SH AUTHOR
This manual page was written by Roberto C. Sanchez <<EMAIL>>,
for the Debian project (but may be used by others).

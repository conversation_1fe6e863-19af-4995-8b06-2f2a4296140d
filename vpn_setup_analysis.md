# VPN设置脚本分析报告

## 脚本概述
`ttttt/vpnsetup_centos.sh` 是一个用于在CentOS/RHEL、Rocky Linux、AlmaLinux和Oracle Linux上自动设置IPsec VPN服务器的脚本。

## 修改/创建的文件清单

### 主要配置文件
| 文件路径 | 描述 | 作用 |
|---------|------|------|
| `/etc/ipsec.conf` | IPsec主配置文件 | 定义IPsec连接参数、加密算法、网络配置 |
| `/etc/ipsec.secrets` | IPsec预共享密钥文件 | 存储PSK认证密钥 |
| `/etc/xl2tpd/xl2tpd.conf` | xl2tpd配置文件 | L2TP隧道服务器配置 |
| `/etc/ppp/options.xl2tpd` | PPP选项文件 | PPP连接参数和DNS设置 |
| `/etc/ppp/chap-secrets` | CHAP认证密钥文件 | L2TP用户认证信息 |
| `/etc/ipsec.d/passwd` | IPsec用户密码文件 | XAUTH用户认证信息 |

### 系统配置文件
| 文件路径 | 描述 | 作用 |
|---------|------|------|
| `/etc/sysctl.conf` | 内核参数配置 | 启用IP转发、优化网络参数 |
| `/etc/sysconfig/iptables` | IPTables规则文件 | 防火墙规则（传统系统） |
| `/etc/sysconfig/nftables.conf` | NFTables规则文件 | 防火墙规则（新系统） |
| `/etc/fail2ban/jail.local` | Fail2Ban配置 | SSH暴力破解防护 |
| `/etc/rc.local` | 开机启动脚本 | 系统启动时自动启动VPN服务 |
| `/etc/firewalld/firewalld.conf` | Firewalld配置 | 可能修改NFTables所有权设置 |
| `/etc/dhcp/dhclient.conf` | DHCP客户端配置 | GCP环境下的MTU修复 |
| `/etc/crypto-policies/back-ends/nss.config` | NSS加密策略配置 | 允许SHA1算法支持 |

### 辅助脚本和目录
| 路径 | 描述 | 作用 |
|------|------|------|
| `/opt/src/` | 源码目录 | 存放下载的源码和脚本 |
| `/opt/src/ikev2.sh` | IKEv2管理脚本 | 管理IKEv2配置和证书 |
| `/opt/src/addvpnuser.sh` | 添加VPN用户脚本 | 添加新的VPN用户 |
| `/opt/src/delvpnuser.sh` | 删除VPN用户脚本 | 删除VPN用户 |
| `/usr/bin/ikev2.sh` | IKEv2脚本符号链接 | 全局访问IKEv2管理 |
| `/usr/bin/addvpnuser.sh` | 添加用户脚本符号链接 | 全局访问用户管理 |
| `/usr/bin/delvpnuser.sh` | 删除用户脚本符号链接 | 全局访问用户管理 |
| `/opt/src/libreswan-$SWAN_VER/Makefile.inc.local` | Libreswan编译配置 | 自定义编译选项 |

### 备份文件
所有被修改的配置文件都会创建带时间戳的备份：
- 格式：`原文件名.old-YYYY-MM-DD_HH_MM_SS`
- 例如：`/etc/ipsec.conf.old-2025-01-15_10_30_45`

## 使用的变量清单

### 用户可配置的环境变量

#### 基本认证变量
| 变量名 | 默认值 | 描述 |
|--------|--------|------|
| `YOUR_IPSEC_PSK` | 空 | 用户设置的IPsec预共享密钥 |
| `YOUR_USERNAME` | 空 | 用户设置的VPN用户名 |
| `YOUR_PASSWORD` | 空 | 用户设置的VPN密码 |
| `VPN_IPSEC_PSK` | 随机生成 | 实际使用的IPsec PSK |
| `VPN_USER` | vpnuser | 实际使用的VPN用户名 |
| `VPN_PASSWORD` | 随机生成 | 实际使用的VPN密码 |

#### 网络配置变量
| 变量名 | 默认值 | 描述 |
|--------|--------|------|
| `VPN_PUBLIC_IP` | 自动检测 | 服务器公网IP地址 |
| `VPN_DNS_SRV1` | ******* | 主DNS服务器 |
| `VPN_DNS_SRV2` | ******* | 备用DNS服务器 |
| `VPN_DNS_NAME` | 空 | 服务器DNS名称（FQDN） |
| `VPN_L2TP_NET` | ************/24 | L2TP网络段 |
| `VPN_L2TP_LOCAL` | ************ | L2TP本地IP |
| `VPN_L2TP_POOL` | *************-************** | L2TP IP池 |
| `VPN_XAUTH_NET` | ************/24 | XAUTH网络段 |
| `VPN_XAUTH_POOL` | *************-************** | XAUTH IP池 |

#### 高级配置变量
| 变量名 | 默认值 | 描述 |
|--------|--------|------|
| `VPN_SWAN_VER` | 自动检测 | 指定Libreswan版本 |
| `VPN_SKIP_IKEV2` | 空 | 设置为yes跳过IKEv2设置 |
| `VPN_CLIENT_NAME` | 空 | IKEv2客户端名称 |
| `VPN_PROTECT_CONFIG` | 空 | 保护配置文件 |
| `VPN_CLIENT_VALIDITY` | 空 | 客户端证书有效期 |

### 内部变量

#### 系统检测变量
| 变量名 | 描述 |
|--------|------|
| `SYS_DT` | 系统时间戳，用于备份文件命名 |
| `NET_IFACE` | 检测到的默认网络接口名 |
| `public_ip` | 检测到的服务器公网IP |
| `os_type` | 操作系统类型（centos/rhel/rocky/alma/ol） |
| `os_ver` | 操作系统版本号 |
| `SWAN_VER` | 使用的Libreswan版本 |
| `use_nft` | 是否使用nftables（0/1） |

#### 配置处理变量
| 变量名 | 描述 |
|--------|------|
| `L2TP_NET` | 处理后的L2TP网络段 |
| `L2TP_LOCAL` | 处理后的L2TP本地IP |
| `L2TP_POOL` | 处理后的L2TP IP池 |
| `XAUTH_NET` | 处理后的XAUTH网络段 |
| `XAUTH_POOL` | 处理后的XAUTH IP池 |
| `DNS_SRV1` | 处理后的主DNS |
| `DNS_SRV2` | 处理后的备用DNS |
| `DNS_SRVS` | 格式化的DNS服务器字符串 |
| `VPN_PASSWORD_ENC` | 加密后的VPN密码 |

## 安装的软件包

### 基础包
- wget, bind-utils, openssl, tar
- iptables, iproute, gawk, grep, sed, net-tools

### VPN相关包
- nss-devel, nspr-devel, pkgconfig, pam-devel
- libcap-ng-devel, libselinux-devel, curl-devel, nss-tools
- flex, bison, gcc, make, util-linux, ppp
- xl2tpd
- systemd-devel, libevent-devel
- fipscheck-devel (CentOS 7)
- iptables-services (CentOS 7) 或 nftables (CentOS 8+)
- fail2ban

### 编译安装
- Libreswan (从源码编译)

## 服务配置

### 启用的服务
- ipsec
- xl2tpd
- fail2ban
- nftables (新系统) 或 iptables (旧系统)

### 禁用的服务
- firewalld (被屏蔽)

## 网络配置

### 开放的端口
- UDP 500 (IKE)
- UDP 4500 (IPsec NAT-T)
- UDP 1701 (L2TP)

### 内核参数优化
- 启用IP转发
- 禁用ICMP重定向
- 优化TCP缓冲区
- 启用BBR拥塞控制（如果支持）

## 安全配置

### 防火墙规则
- 允许IPsec和L2TP流量
- 配置NAT转发
- 保护L2TP端口（仅允许IPsec保护的连接）

### Fail2Ban保护
- SSH暴力破解防护
- 支持iptables和nftables

## 使用说明

1. 运行VPN设置脚本后，使用测试脚本验证配置：
   ```bash
   sudo bash vpn_setup_test_output.sh
   ```

2. 保存测试报告：
   ```bash
   sudo bash vpn_setup_test_output.sh > vpn_test_report.txt
   ```

3. 管理VPN用户：
   ```bash
   sudo addvpnuser.sh    # 添加用户
   sudo delvpnuser.sh    # 删除用户
   sudo ikev2.sh         # 管理IKEv2
   ```

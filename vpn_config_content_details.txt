VPN配置文件具体内容详解
========================

1. /etc/ipsec.conf 配置内容
---------------------------
脚本生成的完整内容：

version 2.0

config setup
  ikev1-policy=accept
  virtual-private=%v4:10.0.0.0/8,%v4:***********/16,%v4:**********/12,%v4:!$L2TP_NET,%v4:!$XAUTH_NET
  uniqueids=no

conn shared
  left=%defaultroute
  leftid=$public_ip
  right=%any
  encapsulation=yes
  authby=secret
  pfs=no
  rekey=no
  keyingtries=5
  dpddelay=30
  dpdtimeout=300
  dpdaction=clear
  ikev2=never
  ike=aes256-sha2;modp2048,aes128-sha2;modp2048,aes256-sha1;modp2048,aes128-sha1;modp2048
  phase2alg=aes_gcm-null,aes128-sha1,aes256-sha1,aes256-sha2_512,aes128-sha2,aes256-sha2
  ikelifetime=24h
  salifetime=24h
  sha2-truncbug=no

conn l2tp-psk
  auto=add
  leftprotoport=17/1701
  rightprotoport=17/%any
  type=transport
  also=shared

conn xauth-psk
  auto=add
  leftsubnet=0.0.0.0/0
  rightaddresspool=$XAUTH_POOL
  modecfgdns=$DNS_SRVS
  leftxauthserver=yes
  rightxauthclient=yes
  leftmodecfgserver=yes
  rightmodecfgclient=yes
  modecfgpull=yes
  cisco-unity=yes
  also=shared

include /etc/ipsec.d/*.conf

2. /etc/ipsec.secrets 配置内容
-----------------------------
脚本生成的内容：

%any  %any  : PSK "$VPN_IPSEC_PSK"

3. /etc/xl2tpd/xl2tpd.conf 配置内容
----------------------------------
脚本生成的完整内容：

[global]
port = 1701

[lns default]
ip range = $L2TP_POOL
local ip = $L2TP_LOCAL
require chap = yes
refuse pap = yes
require authentication = yes
name = l2tpd
pppoptfile = /etc/ppp/options.xl2tpd
length bit = yes

4. /etc/ppp/options.xl2tpd 配置内容
----------------------------------
脚本生成的完整内容：

+mschap-v2
ipcp-accept-local
ipcp-accept-remote
noccp
auth
mtu 1280
mru 1280
proxyarp
lcp-echo-failure 4
lcp-echo-interval 30
connect-delay 5000
ms-dns $DNS_SRV1
ms-dns $DNS_SRV2

5. /etc/ppp/chap-secrets 配置内容
--------------------------------
脚本生成的内容：

"$VPN_USER" l2tpd "$VPN_PASSWORD" *

6. /etc/ipsec.d/passwd 配置内容
------------------------------
脚本生成的内容：

$VPN_USER:$VPN_PASSWORD_ENC:xauth-psk

7. /etc/sysctl.conf 追加内容
----------------------------
脚本追加的内容：

# Added by hwdsl2 VPN script
kernel.msgmnb = 65536
kernel.msgmax = 65536

net.ipv4.ip_forward = 1
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.all.rp_filter = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.conf.default.rp_filter = 0
net.ipv4.conf.$NET_IFACE.send_redirects = 0
net.ipv4.conf.$NET_IFACE.rp_filter = 0

net.core.wmem_max = 16777216
net.core.rmem_max = 16777216
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 87380 16777216

# 如果支持BBR，还会添加：
net.core.default_qdisc = fq
net.ipv4.tcp_congestion_control = bbr

8. /etc/fail2ban/jail.local 配置内容
-----------------------------------
脚本创建的内容：

[ssh-iptables]
enabled = true
filter = sshd
logpath = /var/log/secure

# 对于nftables系统：
port = ssh
banaction = nftables-multiport[blocktype=drop]

# 对于iptables系统：
action = iptables[name=SSH, port=ssh, protocol=tcp]

9. /etc/rc.local 追加内容
------------------------
脚本追加的内容：

# Added by hwdsl2 VPN script
(sleep 15
service ipsec restart
service xl2tpd restart
echo 1 > /proc/sys/net/ipv4/ip_forward)&

10. 防火墙规则内容
-----------------
脚本执行的iptables命令：

# INPUT规则
iptables -I INPUT 1 -p udp --dport 1701 -m policy --dir in --pol none -j DROP
iptables -I INPUT 2 -m conntrack --ctstate INVALID -j DROP
iptables -I INPUT 3 -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
iptables -I INPUT 4 -p udp -m multiport --dports 500,4500 -j ACCEPT
iptables -I INPUT 5 -p udp --dport 1701 -m policy --dir in --pol ipsec -j ACCEPT
iptables -I INPUT 6 -p udp --dport 1701 -j DROP

# FORWARD规则
iptables -I FORWARD 1 -m conntrack --ctstate INVALID -j DROP
iptables -I FORWARD 2 -i $NET_IFACE -o ppp+ -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
iptables -I FORWARD 3 -i ppp+ -o $NET_IFACE -j ACCEPT
iptables -I FORWARD 4 -i ppp+ -o ppp+ -j ACCEPT
iptables -I FORWARD 5 -i $NET_IFACE -d $XAUTH_NET -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
iptables -I FORWARD 6 -s $XAUTH_NET -o $NET_IFACE -j ACCEPT
iptables -I FORWARD 7 -s $XAUTH_NET -o ppp+ -j ACCEPT

# NAT规则
iptables -t nat -I POSTROUTING -s $XAUTH_NET -o $NET_IFACE -m policy --dir out --pol none -j MASQUERADE
iptables -t nat -I POSTROUTING -s $L2TP_NET -o $NET_IFACE -j MASQUERADE

11. Libreswan编译配置
--------------------
/opt/src/libreswan-$SWAN_VER/Makefile.inc.local 内容：

WERROR_CFLAGS=-w -s
USE_DNSSEC=false
USE_DH2=true
USE_NSS_KDF=false
USE_LINUX_AUDIT=false
USE_SECCOMP=false
FINALNSSDIR=/etc/ipsec.d
NSSDIR=/etc/ipsec.d

# 如果需要，还会添加：
USE_XFRM_INTERFACE_IFLA_HEADER=true

12. 条件性修改的文件内容
-----------------------

/etc/firewalld/firewalld.conf 修改：
- 查找：NftablesTableOwner=yes
- 替换为：NftablesTableOwner=no

/etc/dhcp/dhclient.conf 追加（仅GCP环境）：
interface "$NET_IFACE" {
default interface-mtu 1500;
supersede interface-mtu 1500;
}

/etc/crypto-policies/back-ends/nss.config 修改：
- 在"ALL allow="行中添加SHA1:支持

变量替换说明：
=============
脚本中的变量会被实际值替换：

$VPN_IPSEC_PSK - 用户设置或随机生成的PSK
$VPN_USER - 用户设置的用户名或默认"vpnuser"
$VPN_PASSWORD - 用户设置或随机生成的密码
$VPN_PASSWORD_ENC - MD5加密后的密码
$public_ip - 检测到的服务器公网IP
$L2TP_NET - L2TP网络段（默认************/24）
$L2TP_LOCAL - L2TP本地IP（默认************）
$L2TP_POOL - L2TP IP池（默认************0-**************）
$XAUTH_NET - XAUTH网络段（默认************/24）
$XAUTH_POOL - XAUTH IP池（默认*************-**************）
$DNS_SRV1 - 主DNS服务器（默认*******）
$DNS_SRV2 - 备用DNS服务器（默认*******）
$DNS_SRVS - 格式化的DNS字符串
$NET_IFACE - 检测到的网络接口名
$SWAN_VER - Libreswan版本号
$SYS_DT - 系统时间戳（用于备份文件名）

文件权限设置：
=============
chmod 600: /etc/ipsec.secrets* /etc/ppp/chap-secrets* /etc/ipsec.d/passwd*
chmod 755: /etc/rc.local /opt/src/*.sh
chmod +x: 所有下载的脚本文件

SELinux上下文恢复：
==================
restorecon /etc/ipsec.d/*db
restorecon /usr/local/sbin -Rv
restorecon /usr/local/libexec/ipsec -Rv

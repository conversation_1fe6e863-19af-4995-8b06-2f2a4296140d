VPN设置脚本变量和文件清单
=====================================

修改/创建的文件列表：
---------------------

主要配置文件：
1. /etc/ipsec.conf - IPsec主配置文件
2. /etc/ipsec.secrets - IPsec预共享密钥文件
3. /etc/xl2tpd/xl2tpd.conf - xl2tpd配置文件
4. /etc/ppp/options.xl2tpd - PPP选项文件
5. /etc/ppp/chap-secrets - CHAP认证密钥文件
6. /etc/ipsec.d/passwd - IPsec用户密码文件

系统配置文件：
7. /etc/sysctl.conf - 内核参数配置
8. /etc/sysconfig/iptables - IPTables规则文件
9. /etc/sysconfig/nftables.conf - NFTables规则文件
10. /etc/fail2ban/jail.local - Fail2Ban配置
11. /etc/rc.local - 开机启动脚本
12. /etc/firewalld/firewalld.conf - Firewalld配置（可能修改）
13. /etc/dhcp/dhclient.conf - DHCP客户端配置（GCP环境）
14. /etc/crypto-policies/back-ends/nss.config - NSS加密策略配置

辅助脚本和目录：
15. /opt/src/ - 源码目录
16. /opt/src/ikev2.sh - IKEv2管理脚本
17. /opt/src/addvpnuser.sh - 添加VPN用户脚本
18. /opt/src/delvpnuser.sh - 删除VPN用户脚本
19. /usr/bin/ikev2.sh - IKEv2脚本符号链接
20. /usr/bin/addvpnuser.sh - 添加用户脚本符号链接
21. /usr/bin/delvpnuser.sh - 删除用户脚本符号链接
22. /opt/src/libreswan-$SWAN_VER/Makefile.inc.local - Libreswan编译配置

备份文件：
所有被修改的配置文件都会创建 .old-时间戳 备份

使用的变量列表：
---------------

用户可配置的环境变量：

基本认证变量：
- YOUR_IPSEC_PSK='' - 用户设置的IPsec预共享密钥
- YOUR_USERNAME='' - 用户设置的VPN用户名
- YOUR_PASSWORD='' - 用户设置的VPN密码
- VPN_IPSEC_PSK - 实际使用的IPsec PSK（从YOUR_IPSEC_PSK设置或随机生成）
- VPN_USER - 实际使用的VPN用户名（从YOUR_USERNAME设置或默认vpnuser）
- VPN_PASSWORD - 实际使用的VPN密码（从YOUR_PASSWORD设置或随机生成）

网络配置变量：
- VPN_PUBLIC_IP - 服务器公网IP地址（自动检测或手动设置）
- VPN_DNS_SRV1 - 主DNS服务器（默认*******）
- VPN_DNS_SRV2 - 备用DNS服务器（默认*******）
- VPN_DNS_NAME - 服务器DNS名称（FQDN）
- VPN_L2TP_NET - L2TP网络段（默认************/24）
- VPN_L2TP_LOCAL - L2TP本地IP（默认************）
- VPN_L2TP_POOL - L2TP IP池（默认************0-**************）
- VPN_XAUTH_NET - XAUTH网络段（默认************/24）
- VPN_XAUTH_POOL - XAUTH IP池（默认*************-**************）

高级配置变量：
- VPN_SWAN_VER - 指定Libreswan版本
- VPN_SKIP_IKEV2 - 设置为yes跳过IKEv2设置
- VPN_CLIENT_NAME - IKEv2客户端名称
- VPN_PROTECT_CONFIG - 保护配置文件
- VPN_CLIENT_VALIDITY - 客户端证书有效期

内部变量：

系统检测变量：
- SYS_DT - 系统时间戳，用于备份文件命名
- NET_IFACE - 检测到的默认网络接口名
- public_ip - 检测到的服务器公网IP
- os_type - 操作系统类型（centos/rhel/rocky/alma/ol）
- os_ver - 操作系统版本号
- SWAN_VER - 使用的Libreswan版本
- use_nft - 是否使用nftables（0/1）

配置处理变量：
- L2TP_NET - 处理后的L2TP网络段
- L2TP_LOCAL - 处理后的L2TP本地IP
- L2TP_POOL - 处理后的L2TP IP池
- XAUTH_NET - 处理后的XAUTH网络段
- XAUTH_POOL - 处理后的XAUTH IP池
- DNS_SRV1 - 处理后的主DNS
- DNS_SRV2 - 处理后的备用DNS
- DNS_SRVS - 格式化的DNS服务器字符串
- VPN_PASSWORD_ENC - 加密后的VPN密码

开放的端口：
-----------
- UDP 500 (IKE)
- UDP 4500 (IPsec NAT-T)
- UDP 1701 (L2TP)

安装的主要软件包：
-----------------
- xl2tpd - L2TP守护进程
- fail2ban - 防暴力破解
- libreswan - IPsec实现（从源码编译）
- iptables-services 或 nftables - 防火墙
- 各种开发工具和依赖库

启用的服务：
-----------
- ipsec
- xl2tpd
- fail2ban
- nftables 或 iptables

禁用的服务：
-----------
- firewalld（被屏蔽）

测试脚本使用方法：
-----------------
1. 运行测试：sudo bash vpn_setup_test_output.sh
2. 保存报告：sudo bash vpn_setup_test_output.sh > vpn_test_report.txt

管理命令：
---------
- sudo ikev2.sh - 管理IKEv2配置
- sudo addvpnuser.sh - 添加VPN用户
- sudo delvpnuser.sh - 删除VPN用户

/*
 * Layer 2 Tunnelling Protocol Daemon
 * Copyright (C) 2002 <PERSON>
 *
 * This software is distributed under the terms of the GPL, which you
 * should have receivede along with this source.
 *
 * Defines common to several different files
 */

#ifndef _COMMON_H_
typedef unsigned char _u8;
typedef unsigned short _u16;
typedef unsigned long long _u64;
extern int rand_source;

#ifndef LINUX
# define SOL_IP 0
#endif

#define _COMMON_H_
#endif

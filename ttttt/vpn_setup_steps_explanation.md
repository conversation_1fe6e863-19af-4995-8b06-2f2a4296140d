# VPN设置脚本执行步骤详解

## 脚本概述
`vpnsetup_centos_commented.sh` 是一个完整的IPsec VPN服务器自动安装脚本，包含31个主要执行步骤。

## 详细执行步骤

### 第一阶段：环境检查和验证 (步骤1-11)

#### 1. check_root - 检查root权限
- **作用**：验证脚本是否以root用户身份运行
- **原因**：VPN安装需要修改系统配置文件和安装软件包，必须有管理员权限
- **失败处理**：如果不是root用户，提示使用sudo运行脚本

#### 2. check_vz - 检查虚拟化环境
- **作用**：检测是否在OpenVZ虚拟化环境中运行
- **原因**：OpenVZ不支持IPsec VPN所需的内核模块
- **失败处理**：如果检测到OpenVZ环境，退出安装

#### 3. check_lxc - 检查LXC容器
- **作用**：验证LXC容器是否正确配置了/dev/ppp设备
- **原因**：PPP连接需要/dev/ppp设备支持
- **失败处理**：如果缺少设备，提供配置指导链接

#### 4. check_os - 检查操作系统
- **作用**：识别操作系统类型和版本
- **支持系统**：CentOS/RHEL 7-10、Rocky Linux、AlmaLinux、Oracle Linux
- **检查内容**：
  - 读取`/etc/redhat-release`文件
  - 识别发行版类型（centos/rhel/rocky/alma/ol）
  - 检查版本号（7/8/9/10及Stream版本）
  - 验证是否为已停止支持的版本

#### 5. check_iface - 检查网络接口
- **作用**：确定默认网络接口
- **检查步骤**：
  - 安装iproute包（如果缺少）
  - 通过路由表获取默认接口
  - 检查接口状态
  - 警告无线接口（防止在个人电脑上运行）
- **备用方案**：如果默认接口不可用，尝试使用eth0

#### 6. check_creds - 检查VPN凭据
- **作用**：验证和设置VPN认证信息
- **处理逻辑**：
  - 使用用户设置的凭据（如果提供）
  - 自动生成随机凭据（如果未提供）
  - 验证凭据格式和字符合法性
- **安全检查**：
  - 禁止非ASCII字符
  - 禁止特殊字符（\ " '）

#### 7. check_dns - 检查DNS设置
- **作用**：验证用户指定的DNS服务器IP地址格式
- **验证内容**：使用正则表达式检查IPv4地址格式

#### 8. check_server_dns - 检查服务器DNS
- **作用**：验证服务器DNS名称（FQDN）格式
- **用途**：用于IKEv2证书生成

#### 9. check_client_name - 检查客户端名称
- **作用**：验证IKEv2客户端名称格式
- **限制**：
  - 长度不超过64字符
  - 只允许字母、数字、下划线、连字符
  - 不能以连字符开头

#### 10. check_subnets - 检查子网设置
- **作用**：验证自定义VPN子网配置（升级场景）
- **检查内容**：确保自定义子网与初始安装匹配

#### 11. check_libreswan - 检查Libreswan
- **作用**：检查是否已安装Libreswan及版本
- **检查逻辑**：
  - 检查安装目录和二进制文件
  - 验证版本号
  - 确定是否需要重新安装

### 第二阶段：系统准备和软件安装 (步骤12-22)

#### 12. start_setup - 开始安装设置
- **作用**：创建工作目录并显示开始信息
- **操作**：创建`/opt/src`目录并切换到该目录

#### 13. install_setup_pkgs - 安装基础软件包
- **作用**：安装安装过程中需要的基础工具
- **软件包**：wget、bind-utils、openssl、tar、iptables、iproute、gawk、grep、sed、net-tools

#### 14. detect_ip - 检测公网IP
- **作用**：获取服务器的公网IP地址
- **检测方法**：
  - 使用用户指定的IP
  - 通过路由表获取默认IP
  - 通过外部服务查询（OpenDNS、icanhazip.com等）

#### 15. add_epel_repo - 添加EPEL源
- **作用**：添加Extra Packages for Enterprise Linux软件源
- **原因**：某些VPN软件包只在EPEL源中提供

#### 16. install_vpn_pkgs_1 - 安装VPN软件包(第一阶段)
- **作用**：安装编译Libreswan所需的开发工具和库
- **软件包**：nss-devel、nspr-devel、pkgconfig、pam-devel、libcap-ng-devel、libselinux-devel、curl-devel、nss-tools、flex、bison、gcc、make、util-linux、ppp

#### 17. install_vpn_pkgs_2 - 安装VPN软件包(第二阶段)
- **作用**：从EPEL源安装xl2tpd（L2TP守护进程）

#### 18. install_vpn_pkgs_3 - 安装VPN软件包(第三阶段)
- **作用**：安装系统服务和防火墙相关软件包
- **软件包**：systemd-devel、libevent-devel、fipscheck-devel（CentOS 7）、iptables-services或nftables
- **逻辑**：根据系统版本选择iptables或nftables

#### 19. install_fail2ban - 安装Fail2Ban
- **作用**：安装SSH暴力破解防护工具
- **配置**：创建基础的SSH保护规则

#### 20. get_helper_scripts - 下载辅助脚本
- **作用**：下载VPN管理辅助脚本
- **脚本**：
  - ikev2setup.sh → ikev2.sh（IKEv2管理）
  - add_vpn_user.sh → addvpnuser.sh（添加用户）
  - del_vpn_user.sh → delvpnuser.sh（删除用户）
- **操作**：设置可执行权限并创建符号链接到/usr/bin

#### 21. get_libreswan - 下载Libreswan
- **作用**：下载Libreswan IPsec实现的源码
- **版本选择**：自动获取最新推荐版本或使用用户指定版本
- **下载源**：GitHub或官方网站

#### 22. install_libreswan - 编译安装Libreswan
- **作用**：编译和安装Libreswan
- **配置**：创建自定义编译配置文件
- **优化**：使用多核并行编译
- **验证**：检查安装是否成功

### 第三阶段：VPN配置 (步骤23-27)

#### 23. create_vpn_config - 创建VPN配置文件
- **作用**：生成所有VPN相关的配置文件
- **配置文件**：
  - `/etc/ipsec.conf`：IPsec主配置
  - `/etc/ipsec.secrets`：预共享密钥
  - `/etc/xl2tpd/xl2tpd.conf`：L2TP配置
  - `/etc/ppp/options.xl2tpd`：PPP选项
  - `/etc/ppp/chap-secrets`：L2TP用户认证
  - `/etc/ipsec.d/passwd`：XAUTH用户认证

#### 24. update_sysctl - 更新系统参数
- **作用**：优化内核网络参数
- **配置内容**：
  - 启用IP转发
  - 禁用ICMP重定向
  - 优化TCP缓冲区
  - 启用BBR拥塞控制（如果支持）

#### 25. update_iptables - 配置防火墙规则
- **作用**：设置防火墙规则保护VPN服务
- **规则内容**：
  - 开放VPN端口（UDP 500、4500、1701）
  - 配置NAT转发
  - 保护L2TP流量（仅允许IPsec保护的连接）
- **支持**：同时支持iptables和nftables

#### 26. fix_nss_config - 修复NSS配置
- **作用**：修改NSS加密策略以支持SHA1算法
- **原因**：某些旧客户端需要SHA1支持

#### 27. apply_gcp_mtu_fix - 应用GCP MTU修复
- **作用**：修复Google Cloud Platform环境下的MTU问题
- **检测**：通过DMI信息检测GCP环境
- **修复**：设置MTU为1500并修改DHCP配置

### 第四阶段：服务启动和完成 (步骤28-31)

#### 28. enable_on_boot - 配置开机自启
- **作用**：配置VPN服务开机自动启动
- **操作**：
  - 屏蔽firewalld服务
  - 启用iptables/nftables和fail2ban服务
  - 配置rc.local启动脚本

#### 29. start_services - 启动VPN服务
- **作用**：启动所有VPN相关服务
- **操作**：
  - 应用sysctl配置
  - 设置文件权限
  - 恢复SELinux上下文
  - 应用防火墙规则
  - 启动ipsec、xl2tpd、fail2ban服务

#### 30. show_vpn_info - 显示连接信息
- **作用**：显示VPN连接所需的信息
- **内容**：服务器IP、PSK、用户名、密码、客户端配置指南

#### 31. set_up_ikev2 - 设置IKEv2
- **作用**：自动配置IKEv2（如果可用且未跳过）
- **条件**：ikev2.sh脚本存在且未配置过IKEv2
- **环境变量**：传递相关配置参数给IKEv2设置脚本

## 错误处理机制

### 备份策略
- 所有配置文件修改前自动创建带时间戳的备份
- 备份格式：`原文件名.old-YYYY-MM-DD_HH_MM_SS`

### 失败处理
- 每个关键步骤都有错误检查
- 软件包安装失败会重试
- 提供详细的错误信息和解决建议

### 兼容性处理
- 支持多种Linux发行版
- 自动检测系统特性（nftables支持、BBR支持等）
- 针对不同环境（GCP、LXC等）的特殊处理

## 安全特性

### 权限控制
- 密钥文件设置为600权限（仅root可读写）
- 脚本文件设置为755权限（可执行）

### 网络安全
- L2TP流量必须经过IPsec加密
- 配置Fail2Ban防止SSH暴力破解
- 使用强加密算法（AES256、SHA2）

### 配置验证
- 验证所有用户输入
- 检查IP地址和域名格式
- 确保凭据不包含危险字符

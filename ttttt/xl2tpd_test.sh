#!/bin/bash
#
# L2TP/IPsec VPN服务器测试脚本
# 用于验证xl2tpd_only_setup.sh安装的VPN服务器状态
#

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出函数
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# 检查是否以root权限运行
check_root() {
    if [ "$(id -u)" != 0 ]; then
        print_error "此脚本需要root权限运行。请使用 'sudo bash $0'"
        exit 1
    fi
}

# 检查配置文件
check_config_files() {
    print_header "检查L2TP VPN配置文件"
    
    local files=(
        "/etc/ipsec.conf:IPsec主配置文件"
        "/etc/ipsec.secrets:IPsec预共享密钥文件"
        "/etc/xl2tpd/xl2tpd.conf:xl2tpd配置文件"
        "/etc/ppp/options.xl2tpd:PPP选项文件"
        "/etc/ppp/chap-secrets:用户认证文件"
    )
    
    for item in "${files[@]}"; do
        local file="${item%%:*}"
        local desc="${item##*:}"
        
        if [ -f "$file" ]; then
            print_success "$desc 存在"
            # 检查文件是否包含L2TP相关配置
            if [ "$file" = "/etc/ipsec.conf" ]; then
                if grep -q "conn l2tp-psk" "$file"; then
                    print_success "  包含L2TP连接配置"
                else
                    print_warning "  缺少L2TP连接配置"
                fi
            fi
        else
            print_error "$desc 不存在: $file"
        fi
    done
}

# 检查服务状态
check_services() {
    print_header "检查VPN服务状态"
    
    local services=(
        "ipsec:IPsec服务"
        "xl2tpd:L2TP服务"
        "fail2ban:Fail2Ban服务"
    )
    
    for item in "${services[@]}"; do
        local service="${item%%:*}"
        local desc="${item##*:}"
        
        if systemctl is-active --quiet "$service"; then
            print_success "$desc 正在运行"
        else
            print_error "$desc 未运行"
            echo "  尝试启动服务: sudo systemctl start $service"
        fi
        
        if systemctl is-enabled --quiet "$service"; then
            print_success "$desc 已启用开机自启"
        else
            print_warning "$desc 未启用开机自启"
        fi
    done
}

# 检查端口监听
check_ports() {
    print_header "检查VPN端口监听状态"
    
    local ports=(
        "500:UDP:IKE协议端口"
        "4500:UDP:IPsec NAT-T端口"
        "1701:UDP:L2TP端口"
    )
    
    for item in "${ports[@]}"; do
        local port="${item%%:*}"
        local proto="${item#*:}"
        proto="${proto%%:*}"
        local desc="${item##*:}"
        
        if ss -ulnp 2>/dev/null | grep -q ":$port "; then
            print_success "$desc ($proto $port) 正在监听"
            # 显示监听进程
            local process=$(ss -ulnp 2>/dev/null | grep ":$port " | awk '{print $NF}' | head -1)
            echo "  进程: $process"
        else
            print_error "$desc ($proto $port) 未监听"
        fi
    done
}

# 检查网络配置
check_network() {
    print_header "检查网络配置"
    
    # 检查IP转发
    local ip_forward=$(cat /proc/sys/net/ipv4/ip_forward 2>/dev/null)
    if [ "$ip_forward" = "1" ]; then
        print_success "IP转发已启用"
    else
        print_error "IP转发未启用"
        echo "  手动启用: echo 1 > /proc/sys/net/ipv4/ip_forward"
    fi
    
    # 检查默认网络接口
    local def_iface=$(ip route | grep default | head -1 | awk '{print $5}')
    if [ -n "$def_iface" ]; then
        print_success "默认网络接口: $def_iface"
        
        # 检查接口状态
        local iface_state=$(cat "/sys/class/net/$def_iface/operstate" 2>/dev/null)
        if [ "$iface_state" = "up" ]; then
            print_success "网络接口状态: UP"
        else
            print_warning "网络接口状态: $iface_state"
        fi
    else
        print_error "无法检测默认网络接口"
    fi
    
    # 检查公网IP
    print_info "检测公网IP地址..."
    local public_ip=$(wget -t 2 -T 10 -qO- http://ipv4.icanhazip.com 2>/dev/null)
    if [ -n "$public_ip" ]; then
        print_success "公网IP: $public_ip"
    else
        print_warning "无法检测公网IP"
    fi
}

# 检查防火墙规则
check_firewall() {
    print_header "检查防火墙规则"
    
    # 检查iptables规则
    if command -v iptables >/dev/null 2>&1; then
        print_info "检查iptables规则..."
        
        # 检查INPUT规则
        if iptables -L INPUT -n | grep -q "udp dpt:500"; then
            print_success "IKE端口(500)规则已配置"
        else
            print_warning "IKE端口(500)规则未找到"
        fi
        
        if iptables -L INPUT -n | grep -q "udp dpt:4500"; then
            print_success "NAT-T端口(4500)规则已配置"
        else
            print_warning "NAT-T端口(4500)规则未找到"
        fi
        
        if iptables -L INPUT -n | grep -q "udp dpt:1701"; then
            print_success "L2TP端口(1701)规则已配置"
        else
            print_warning "L2TP端口(1701)规则未找到"
        fi
        
        # 检查NAT规则
        if iptables -t nat -L POSTROUTING -n | grep -q "MASQUERADE"; then
            print_success "NAT转发规则已配置"
        else
            print_warning "NAT转发规则未找到"
        fi
    fi
    
    # 检查nftables规则
    if command -v nft >/dev/null 2>&1; then
        print_info "检查nftables规则..."
        if nft list ruleset 2>/dev/null | grep -q "udp dport { 500, 4500, 1701 }"; then
            print_success "nftables VPN规则已配置"
        else
            print_warning "nftables VPN规则未找到"
        fi
    fi
}

# 检查VPN配置详情
check_vpn_config() {
    print_header "检查VPN配置详情"
    
    # 检查L2TP网络配置
    if [ -f "/etc/xl2tpd/xl2tpd.conf" ]; then
        local ip_range=$(grep "ip range" /etc/xl2tpd/xl2tpd.conf | awk -F'=' '{print $2}' | tr -d ' ')
        local local_ip=$(grep "local ip" /etc/xl2tpd/xl2tpd.conf | awk -F'=' '{print $2}' | tr -d ' ')
        
        if [ -n "$ip_range" ]; then
            print_success "L2TP客户端IP池: $ip_range"
        fi
        
        if [ -n "$local_ip" ]; then
            print_success "L2TP服务器本地IP: $local_ip"
        fi
    fi
    
    # 检查DNS配置
    if [ -f "/etc/ppp/options.xl2tpd" ]; then
        local dns_servers=$(grep "ms-dns" /etc/ppp/options.xl2tpd | awk '{print $2}' | tr '\n' ' ')
        if [ -n "$dns_servers" ]; then
            print_success "DNS服务器: $dns_servers"
        fi
    fi
    
    # 检查用户数量
    if [ -f "/etc/ppp/chap-secrets" ]; then
        local user_count=$(grep -v "^#" /etc/ppp/chap-secrets | grep -v "^$" | wc -l)
        print_success "配置的VPN用户数量: $user_count"
        
        # 显示用户名（不显示密码）
        print_info "VPN用户列表:"
        grep -v "^#" /etc/ppp/chap-secrets | grep -v "^$" | awk '{print "  - " $1}' | tr -d '"'
    fi
}

# 运行连接测试
run_connection_test() {
    print_header "运行VPN连接测试"
    
    # 检查IPsec状态
    if command -v ipsec >/dev/null 2>&1; then
        print_info "IPsec状态检查:"
        local ipsec_status=$(ipsec status 2>/dev/null)
        if [ -n "$ipsec_status" ]; then
            echo "$ipsec_status" | head -5
        else
            print_warning "无法获取IPsec状态"
        fi
        
        print_info "IPsec验证:"
        ipsec verify 2>/dev/null | head -10 || print_warning "IPsec验证失败"
    fi
    
    # 检查xl2tpd进程
    if pgrep xl2tpd >/dev/null; then
        print_success "xl2tpd进程正在运行"
        local xl2tpd_pid=$(pgrep xl2tpd)
        print_info "xl2tpd进程ID: $xl2tpd_pid"
    else
        print_error "xl2tpd进程未运行"
    fi
}

# 生成连接信息
show_connection_info() {
    print_header "VPN连接信息"
    
    # 获取公网IP
    local public_ip=$(wget -t 2 -T 10 -qO- http://ipv4.icanhazip.com 2>/dev/null)
    [ -z "$public_ip" ] && public_ip="<检测失败>"
    
    # 获取PSK（如果可读）
    local psk="<无法读取>"
    if [ -r "/etc/ipsec.secrets" ]; then
        psk=$(grep "PSK" /etc/ipsec.secrets | awk -F'"' '{print $2}' | head -1)
    fi
    
    # 获取用户信息
    local username="<无法读取>"
    local password="<无法读取>"
    if [ -r "/etc/ppp/chap-secrets" ]; then
        local user_line=$(grep -v "^#" /etc/ppp/chap-secrets | grep -v "^$" | head -1)
        if [ -n "$user_line" ]; then
            username=$(echo "$user_line" | awk '{print $1}' | tr -d '"')
            password=$(echo "$user_line" | awk '{print $3}' | tr -d '"')
        fi
    fi
    
    echo ""
    echo "请使用以下信息连接到您的L2TP/IPsec VPN："
    echo ""
    echo "服务器IP: $public_ip"
    echo "协议类型: L2TP/IPsec (预共享密钥)"
    echo "IPsec PSK: $psk"
    echo "用户名: $username"
    echo "密码: $password"
    echo ""
    echo "客户端配置指南: https://vpnsetup.net/clients"
    echo ""
}

# 主函数
main() {
    print_header "L2TP/IPsec VPN服务器测试报告"
    
    echo "测试时间: $(date)"
    echo "操作系统: $(cat /etc/redhat-release 2>/dev/null || echo '未知')"
    echo "内核版本: $(uname -r)"
    echo ""
    
    check_config_files
    echo ""
    
    check_services
    echo ""
    
    check_ports
    echo ""
    
    check_network
    echo ""
    
    check_firewall
    echo ""
    
    check_vpn_config
    echo ""
    
    run_connection_test
    echo ""
    
    show_connection_info
    
    print_header "测试完成"
    echo "如需保存此报告，请运行: sudo bash $0 > l2tp_test_report.txt"
}

# 检查root权限并运行主函数
check_root
main "$@"

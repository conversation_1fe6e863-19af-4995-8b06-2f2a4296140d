# L2TP/IPsec VPN服务器安装脚本

## 脚本简介

`xl2tpd_only_setup.sh` 是一个专门用于安装纯L2TP/IPsec VPN服务器的简化脚本，基于原始的 `vpnsetup_centos.sh` 脚本精简而来。

### 主要特点

- **纯L2TP支持**：仅启用L2TP/IPsec协议，不包含IKEv2和XAUTH
- **简化配置**：移除了复杂的多协议支持，专注于L2TP
- **自动化安装**：一键安装，自动配置所有必要组件
- **中文界面**：完全中文化的安装过程和错误提示
- **安全加固**：包含Fail2Ban防护和防火墙配置

## 支持的系统

- CentOS/RHEL 7-10
- Rocky Linux 8-10
- AlmaLinux 8-10
- Oracle Linux 7-10

**注意**：CentOS 7和8已停止支持，建议使用Rocky Linux或AlmaLinux。

## 安装前准备

### 系统要求

1. **服务器环境**：必须在VPS或专用服务器上运行
2. **Root权限**：需要管理员权限
3. **网络连接**：需要互联网连接下载软件包
4. **防火墙**：确保以下端口可访问：
   - UDP 500 (IKE)
   - UDP 4500 (IPsec NAT-T)
   - UDP 1701 (L2TP)

### 警告

⚠️ **请勿在个人电脑或Mac上运行此脚本！**

脚本会检测无线网络接口，如果发现会自动退出安装。

## 使用方法

### 1. 下载脚本

```bash
wget https://your-server.com/xl2tpd_only_setup.sh
# 或者
curl -O https://your-server.com/xl2tpd_only_setup.sh
```

### 2. 设置VPN凭据（可选）

编辑脚本文件，在顶部配置区域设置您的VPN凭据：

```bash
nano xl2tpd_only_setup.sh
```

修改以下变量：

```bash
# 定义您的VPN凭据（留空则自动生成）
YOUR_IPSEC_PSK='your_preshared_key'    # IPsec预共享密钥
YOUR_USERNAME='your_username'          # VPN用户名
YOUR_PASSWORD='your_password'          # VPN密码
```

### 3. 自定义网络配置（可选）

如果需要自定义网络段，可以修改：

```bash
# L2TP网络配置
VPN_L2TP_NET='************/24'                    # L2TP网络段
VPN_L2TP_LOCAL='************'                     # 服务器本地IP
VPN_L2TP_POOL='************0-**************'     # 客户端IP池

# DNS服务器配置
VPN_DNS_SRV1='*******'                           # 主DNS
VPN_DNS_SRV2='*******'                           # 备用DNS
```

### 4. 运行安装脚本

```bash
chmod +x xl2tpd_only_setup.sh
sudo bash xl2tpd_only_setup.sh
```

### 5. 等待安装完成

安装过程大约需要5-15分钟，取决于网络速度和服务器性能。

## 安装过程

脚本会按以下步骤执行：

1. **环境检查**：验证系统、权限、网络接口
2. **凭据设置**：配置或生成VPN认证信息
3. **IP检测**：自动检测服务器公网IP
4. **软件安装**：安装必要的软件包和依赖
5. **Libreswan编译**：下载并编译IPsec实现
6. **配置创建**：生成VPN配置文件
7. **系统优化**：调整内核参数
8. **防火墙配置**：设置安全规则
9. **服务启动**：启动VPN服务
10. **状态检查**：验证安装结果

## 安装完成后

### 连接信息

安装完成后，脚本会显示连接信息：

```
================================================

L2TP/IPsec VPN服务器安装完成！

请使用以下信息连接到您的VPN：

服务器IP: xxx.xxx.xxx.xxx
IPsec PSK: your_preshared_key
用户名: your_username
密码: your_password

L2TP网络段: ************/24
客户端IP池: ************0-**************
DNS服务器: *******, *******

================================================
```

### 客户端配置

#### Windows 10/11

1. 打开"设置" → "网络和Internet" → "VPN"
2. 点击"添加VPN连接"
3. 选择"Windows (内置)"
4. 填写连接信息：
   - 连接名称：任意名称
   - 服务器名称或地址：服务器IP
   - VPN类型：L2TP/IPsec（预共享密钥）
   - 预共享密钥：IPsec PSK
   - 用户名：VPN用户名
   - 密码：VPN密码

#### macOS

1. 打开"系统偏好设置" → "网络"
2. 点击"+"添加新连接
3. 接口：VPN，VPN类型：L2TP over IPsec
4. 填写连接信息：
   - 服务器地址：服务器IP
   - 账户名称：VPN用户名
   - 密码：VPN密码
   - 机器认证：共享的密钥
   - 共享密钥：IPsec PSK

#### Android

1. 打开"设置" → "网络和连接" → "VPN"
2. 点击"+"添加VPN配置文件
3. 选择"L2TP/IPsec PSK"
4. 填写连接信息：
   - 名称：任意名称
   - 服务器：服务器IP
   - L2TP密钥：留空
   - IPsec预共享密钥：IPsec PSK
   - 用户名：VPN用户名
   - 密码：VPN密码

#### iOS

1. 打开"设置" → "通用" → "VPN"
2. 点击"添加VPN配置"
3. 选择"L2TP"
4. 填写连接信息：
   - 描述：任意名称
   - 服务器：服务器IP
   - 账户：VPN用户名
   - 密码：VPN密码
   - 密钥：IPsec PSK

## 管理和维护

### 检查服务状态

```bash
# 检查IPsec状态
sudo ipsec status

# 检查xl2tpd状态
sudo systemctl status xl2tpd

# 检查fail2ban状态
sudo systemctl status fail2ban

# 查看监听端口
sudo ss -ulnp | grep -E "(500|4500|1701)"
```

### 重启VPN服务

```bash
sudo service ipsec restart
sudo service xl2tpd restart
```

### 查看日志

```bash
# 查看系统日志
sudo journalctl -u ipsec -f
sudo journalctl -u xl2tpd -f

# 查看认证日志
sudo tail -f /var/log/secure
```

### 添加更多用户

编辑用户认证文件：

```bash
sudo nano /etc/ppp/chap-secrets
```

添加新用户行：

```
"new_username" l2tpd "new_password" *
```

### 修改网络配置

如需修改IP池或DNS，编辑相应配置文件后重启服务：

```bash
sudo nano /etc/xl2tpd/xl2tpd.conf
sudo nano /etc/ppp/options.xl2tpd
sudo service xl2tpd restart
```

## 故障排除

### 常见问题

1. **无法连接**：检查防火墙和端口开放情况
2. **认证失败**：验证用户名、密码和PSK是否正确
3. **服务未启动**：检查服务状态并查看日志

### 卸载VPN

如需完全卸载，可以：

1. 停止服务：`sudo systemctl stop ipsec xl2tpd fail2ban`
2. 禁用服务：`sudo systemctl disable ipsec xl2tpd fail2ban`
3. 删除配置文件：`sudo rm -rf /etc/ipsec.* /etc/xl2tpd/ /etc/ppp/options.xl2tpd /etc/ppp/chap-secrets`
4. 恢复备份文件（如果需要）

## 安全注意事项

1. **定期更新**：保持系统和软件包更新
2. **强密码**：使用复杂的PSK和用户密码
3. **监控日志**：定期检查访问日志
4. **限制访问**：考虑使用fail2ban或其他防护措施
5. **备份配置**：定期备份VPN配置文件

## 技术支持

如遇到问题，请：

1. 检查系统日志：`sudo journalctl -xe`
2. 验证网络连接：`ping *******`
3. 检查防火墙状态：`sudo iptables -L -n`
4. 查看服务状态：`sudo systemctl status ipsec xl2tpd`

## 许可证

本脚本基于原始vpnsetup_centos.sh脚本修改，遵循相同的开源许可证。

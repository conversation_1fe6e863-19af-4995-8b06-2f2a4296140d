#!/bin/bash
#
# IPsec VPN服务器自动安装脚本 - CentOS/RHEL/Rocky Linux/AlmaLinux/Oracle Linux版本
# 
# 警告：请勿在个人电脑或Mac上运行此脚本！
#
# 最新版本地址：https://github.com/hwdsl2/setup-ipsec-vpn
#
# <AUTHOR> <EMAIL>
# 基于 Thomas Sarlandie 的工作 (Copyright 2012)
#
# 本作品采用知识共享署名-相同方式共享 3.0 未本地化版本许可协议进行许可
# 许可协议地址：http://creativecommons.org/licenses/by-sa/3.0/
#

# =====================================================
# 用户配置区域 - 请在此处定义您的VPN凭据
# =====================================================

# 定义您自己的IPsec预共享密钥、VPN用户名和密码
# - 所有值必须放在'单引号'内
# - 请勿在值中使用这些特殊字符：\ " '

YOUR_IPSEC_PSK=''    # IPsec预共享密钥（留空则自动生成）
YOUR_USERNAME=''     # VPN用户名（留空则使用默认值vpnuser）
YOUR_PASSWORD=''     # VPN密码（留空则自动生成）

# =====================================================
# 脚本初始化和工具函数定义
# =====================================================

# 设置PATH环境变量，确保能找到所有必要的命令
export PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"

# 生成系统时间戳，用于备份文件命名（格式：YYYY-MM-DD_HH_MM_SS）
SYS_DT=$(date +%F-%T | tr ':' '_')

# 错误处理函数：输出错误信息并退出脚本
exiterr()  { echo "Error: $1" >&2; exit 1; }

# DNF安装失败的专用错误处理函数
exiterr2() { exiterr "'dnf install' failed."; }

# 配置文件备份函数：在修改前创建带时间戳的备份
conf_bk() { /bin/cp -f "$1" "$1.old-$SYS_DT" 2>/dev/null; }

# 大标题输出函数：用于显示安装步骤
bigecho() { echo "## $1"; }

# =====================================================
# 验证和检查函数
# =====================================================

# 检查IP地址格式是否有效
check_ip() {
  # IPv4地址的正则表达式模式
  IP_REGEX='^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$'
  printf '%s' "$1" | tr -d '\n' | grep -Eq "$IP_REGEX"
}

# 检查DNS域名格式是否有效（FQDN格式）
check_dns_name() {
  # 完全限定域名的正则表达式模式
  FQDN_REGEX='^([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$'
  printf '%s' "$1" | tr -d '\n' | grep -Eq "$FQDN_REGEX"
}

# 检查脚本是否以root权限运行
check_root() {
  if [ "$(id -u)" != 0 ]; then
    exiterr "Script must be run as root. Try 'sudo bash $0'"
  fi
}

# 检查是否在OpenVZ虚拟化环境中运行（不支持）
check_vz() {
  if [ -f /proc/user_beancounters ]; then
    exiterr "OpenVZ VPS is not supported."
  fi
}

# 检查LXC容器环境是否正确配置
check_lxc() {
  # shellcheck disable=SC2154
  if [ "$container" = "lxc" ] && [ ! -e /dev/ppp ]; then
cat 1>&2 <<'EOF'
Error: /dev/ppp is missing. LXC containers require configuration.
       See: https://github.com/hwdsl2/setup-ipsec-vpn/issues/1014
EOF
  exit 1
  fi
}

# 检查操作系统类型和版本
check_os() {
  rh_file="/etc/redhat-release"
  if [ -f "$rh_file" ]; then
    # 默认设置为CentOS
    os_type=centos
    
    # 检查具体的发行版类型
    if grep -q "Red Hat" "$rh_file"; then
      os_type=rhel
    fi
    [ -f /etc/oracle-release ] && os_type=ol
    grep -qi rocky "$rh_file" && os_type=rocky
    grep -qi alma "$rh_file" && os_type=alma
    
    # 检查版本号
    if grep -q "release 7" "$rh_file"; then
      os_ver=7
    elif grep -q "release 8" "$rh_file"; then
      os_ver=8
      grep -qi stream "$rh_file" && os_ver=8s  # CentOS Stream 8
    elif grep -q "release 9" "$rh_file"; then
      os_ver=9
      grep -qi stream "$rh_file" && os_ver=9s  # CentOS Stream 9
    elif grep -q "release 10" "$rh_file"; then
      os_ver=10
      grep -qi stream "$rh_file" && os_ver=10s  # CentOS Stream 10
    else
      exiterr "This script only supports CentOS/RHEL 7-10."
    fi
    
    # 检查是否为已停止支持的CentOS版本
    if [ "$os_type" = "centos" ] \
      && { [ "$os_ver" = 7 ] || [ "$os_ver" = 8 ] || [ "$os_ver" = 8s ]; }; then
      exiterr "CentOS Linux $os_ver is EOL and not supported."
    fi
  else
cat 1>&2 <<'EOF'
Error: This script only supports one of the following OS:
       CentOS/RHEL, Rocky Linux, AlmaLinux or Oracle Linux
EOF
    exit 1
  fi
}

# 检查和确定默认网络接口
check_iface() {
  # 如果系统没有route或ip命令，先安装iproute包
  if ! command -v route >/dev/null 2>&1 && ! command -v ip >/dev/null 2>&1; then
    (
      set -x
      dnf -y -q install iproute >/dev/null || dnf -y -q install iproute >/dev/null
    )
  fi
  
  # 尝试获取默认路由的网络接口
  def_iface=$(route 2>/dev/null | grep -m 1 '^default' | grep -o '[^ ]*$')
  [ -z "$def_iface" ] && def_iface=$(ip -4 route list 0/0 2>/dev/null | grep -m 1 -Po '(?<=dev )(\S+)')
  
  # 检查接口状态
  def_state=$(cat "/sys/class/net/$def_iface/operstate" 2>/dev/null)
  if [ -n "$def_state" ] && [ "$def_state" != "down" ]; then
    case $def_iface in
      wl*)
        # 检测到无线接口，警告用户不要在个人电脑上运行
        exiterr "Wireless interface '$def_iface' detected. DO NOT run this script on your PC or Mac!"
        ;;
    esac
    NET_IFACE="$def_iface"
  else
    # 如果默认接口不可用，尝试使用eth0
    eth0_state=$(cat "/sys/class/net/eth0/operstate" 2>/dev/null)
    if [ -z "$eth0_state" ] || [ "$eth0_state" = "down" ]; then
      exiterr "Could not detect the default network interface."
    fi
    NET_IFACE=eth0
  fi
}

# 检查和设置VPN凭据
check_creds() {
  # 如果用户设置了凭据，则使用用户设置的值
  [ -n "$YOUR_IPSEC_PSK" ] && VPN_IPSEC_PSK="$YOUR_IPSEC_PSK"
  [ -n "$YOUR_USERNAME" ] && VPN_USER="$YOUR_USERNAME"
  [ -n "$YOUR_PASSWORD" ] && VPN_PASSWORD="$YOUR_PASSWORD"
  
  # 如果用户没有设置任何凭据，则自动生成
  if [ -z "$VPN_IPSEC_PSK" ] && [ -z "$VPN_USER" ] && [ -z "$VPN_PASSWORD" ]; then
    bigecho "VPN credentials not set by user. Generating random PSK and password..."
    # 生成20字符的随机PSK（排除容易混淆的字符）
    VPN_IPSEC_PSK=$(LC_CTYPE=C tr -dc 'A-HJ-NPR-Za-km-z2-9' </dev/urandom 2>/dev/null | head -c 20)
    VPN_USER=vpnuser
    # 生成16字符的随机密码
    VPN_PASSWORD=$(LC_CTYPE=C tr -dc 'A-HJ-NPR-Za-km-z2-9' </dev/urandom 2>/dev/null | head -c 16)
  fi
  
  # 验证所有凭据都已设置
  if [ -z "$VPN_IPSEC_PSK" ] || [ -z "$VPN_USER" ] || [ -z "$VPN_PASSWORD" ]; then
    exiterr "All VPN credentials must be specified. Edit the script and re-enter them."
  fi
  
  # 检查凭据是否包含非ASCII字符
  if printf '%s' "$VPN_IPSEC_PSK $VPN_USER $VPN_PASSWORD" | LC_ALL=C grep -q '[^ -~]\+'; then
    exiterr "VPN credentials must not contain non-ASCII characters."
  fi
  
  # 检查凭据是否包含特殊字符
  case "$VPN_IPSEC_PSK $VPN_USER $VPN_PASSWORD" in
    *[\\\"\']*)
      exiterr "VPN credentials must not contain these special characters: \\ \" '"
      ;;
  esac
}

# 检查DNS服务器设置
check_dns() {
  if { [ -n "$VPN_DNS_SRV1" ] && ! check_ip "$VPN_DNS_SRV1"; } \
    || { [ -n "$VPN_DNS_SRV2" ] && ! check_ip "$VPN_DNS_SRV2"; }; then
    exiterr "The DNS server specified is invalid."
  fi
}

# 检查服务器DNS名称设置
check_server_dns() {
  if [ -n "$VPN_DNS_NAME" ] && ! check_dns_name "$VPN_DNS_NAME"; then
    exiterr "Invalid DNS name. 'VPN_DNS_NAME' must be a fully qualified domain name (FQDN)."
  fi
}

# 检查客户端名称设置
check_client_name() {
  if [ -n "$VPN_CLIENT_NAME" ]; then
    name_len="$(printf '%s' "$VPN_CLIENT_NAME" | wc -m)"
    # 验证客户端名称长度和字符
    if [ "$name_len" -gt "64" ] || printf '%s' "$VPN_CLIENT_NAME" | LC_ALL=C grep -q '[^A-Za-z0-9_-]\+' \
      || case $VPN_CLIENT_NAME in -*) true ;; *) false ;; esac; then
      exiterr "Invalid client name. Use one word only, no special characters except '-' and '_'."
    fi
  fi
}

# 检查VPN子网设置（用于升级场景）
check_subnets() {
  if [ -s /etc/ipsec.conf ] && grep -qs "hwdsl2 VPN script" /etc/sysctl.conf; then
    # 如果是升级安装，检查自定义子网是否与初始安装匹配
    L2TP_NET=${VPN_L2TP_NET:-'************/24'}
    XAUTH_NET=${VPN_XAUTH_NET:-'************/24'}
    if ! grep -q "$L2TP_NET" /etc/ipsec.conf \
      || ! grep -q "$XAUTH_NET" /etc/ipsec.conf; then
      echo "Error: The custom VPN subnets specified do not match initial install." >&2
      echo "       See Advanced usage -> Customize VPN subnets for more information." >&2
      exit 1
    fi
  fi
}

# =====================================================
# 安装准备和软件包安装函数
# =====================================================

# 开始安装设置
start_setup() {
  bigecho "VPN setup in progress... Please be patient."
  # 创建源码目录并切换到该目录
  mkdir -p /opt/src
  cd /opt/src || exit 1
}

# 安装设置所需的基础软件包
install_setup_pkgs() {
  bigecho "Installing packages required for setup..."
  (
    set -x
    # 安装基础工具包：wget(下载)、bind-utils(DNS工具)、openssl(加密)、tar(解压)
    # iptables(防火墙)、iproute(网络)、gawk/grep/sed(文本处理)、net-tools(网络工具)
    dnf -y -q install wget bind-utils openssl tar \
      iptables iproute gawk grep sed net-tools >/dev/null \
    || dnf -y -q install wget bind-utils openssl tar \
      iptables iproute gawk grep sed net-tools >/dev/null
  ) || exiterr2
}

# 获取服务器的默认IP地址
get_default_ip() {
  # 使用ip命令获取默认路由的源IP
  def_ip=$(ip -4 route get 1 | sed 's/ uid .*//' | awk '{print $NF;exit}' 2>/dev/null)
  # 检查是否为公网IP（排除私有IP段）
  if check_ip "$def_ip" \
    && ! printf '%s' "$def_ip" | grep -Eq '^(10|127|172\.(1[6-9]|2[0-9]|3[0-1])|192\.168|169\.254)\.'; then
    public_ip="$def_ip"
  fi
}

# 检测服务器的公网IP地址
detect_ip() {
  # 首先尝试使用用户指定的IP或获取默认IP
  public_ip=${VPN_PUBLIC_IP:-''}
  check_ip "$public_ip" || get_default_ip
  check_ip "$public_ip" && return 0
  
  bigecho "Trying to auto discover IP of this server..."
  # 尝试通过外部服务获取公网IP
  check_ip "$public_ip" || public_ip=$(dig @resolver1.opendns.com -t A -4 myip.opendns.com +short)
  check_ip "$public_ip" || public_ip=$(wget -t 2 -T 10 -qO- http://ipv4.icanhazip.com)
  check_ip "$public_ip" || public_ip=$(wget -t 2 -T 10 -qO- http://ip1.dynupdate.no-ip.com)
  check_ip "$public_ip" || exiterr "Cannot detect this server's public IP. Define it as variable 'VPN_PUBLIC_IP' and re-run this script."
}

# 添加EPEL软件源
add_epel_repo() {
  bigecho "Adding the EPEL repository..."
  # 构建EPEL源的下载URL
  epel_url="https://dl.fedoraproject.org/pub/epel/epel-release-latest-$(rpm -E '%{rhel}').noarch.rpm"
  (
    set -x
    # 尝试安装EPEL源
    dnf -y -q install epel-release >/dev/null 2>&1 || dnf -y -q install "$epel_url" >/dev/null
  ) || exiterr2
}

# 安装VPN相关软件包 - 第一阶段（开发工具和依赖库）
install_vpn_pkgs_1() {
  bigecho "Installing packages required for the VPN..."
  # 设置软件源启用参数
  erp="--enablerepo"
  rp1="$erp=epel"
  rp2="$erp=*server-*optional*"
  rp3="$erp=*releases-optional*"

  # 针对Oracle Linux的特殊软件源配置
  if [ "$os_type" = "ol" ]; then
    if [ "$os_ver" = 9 ]; then
      rp1="$erp=ol9_developer_EPEL"
    elif [ "$os_ver" = 8 ]; then
      rp1="$erp=ol8_developer_EPEL"
    else
      rp3="$erp=ol7_optional_latest"
    fi
  fi

  (
    set -x
    # 安装编译Libreswan所需的开发工具和库
    # nss-devel/nspr-devel: NSS加密库开发包
    # pkgconfig: 包配置工具
    # pam-devel: PAM认证开发包
    # libcap-ng-devel: 权限管理库
    # libselinux-devel: SELinux开发包
    # curl-devel: cURL开发包
    # nss-tools: NSS工具
    # flex/bison: 词法和语法分析器
    # gcc/make: 编译工具
    # util-linux: 系统工具
    # ppp: PPP协议支持
    dnf -y -q install nss-devel nspr-devel pkgconfig pam-devel \
      libcap-ng-devel libselinux-devel curl-devel nss-tools \
      flex bison gcc make util-linux ppp >/dev/null
  ) || exiterr2
}

# 安装VPN相关软件包 - 第二阶段（xl2tpd）
install_vpn_pkgs_2() {
  (
    set -x
    # 从EPEL源安装xl2tpd（L2TP守护进程）
    dnf "$rp1" -y -q install xl2tpd >/dev/null 2>&1
  ) || exiterr2
}

# 安装VPN相关软件包 - 第三阶段（系统服务和防火墙）
install_vpn_pkgs_3() {
  use_nft=0  # 标记是否使用nftables

  # 定义要安装的软件包
  p1=systemd-devel      # systemd开发包
  p2=libevent-devel     # 事件库开发包
  p3=fipscheck-devel    # FIPS检查开发包
  p4=iptables-services  # iptables服务包

  if [ "$os_ver" = 7 ]; then
    # CentOS 7使用传统的iptables
    (
      set -x
      dnf "$rp2" "$rp3" -y -q install $p1 $p2 $p3 $p4 >/dev/null
    ) || exiterr2
  else
    # CentOS 8+首先安装基础包
    (
      set -x
      dnf -y -q install $p1 $p2 >/dev/null
    ) || exiterr2

    # 检查是否需要使用nftables
    if [ "$os_ver" = 9 ] || [ "$os_ver" = 9s ] \
      || [ "$os_ver" = 10 ] || [ "$os_ver" = 10s ] \
      || systemctl is-active --quiet firewalld \
      || systemctl is-active --quiet nftables \
      || grep -qs "hwdsl2 VPN script" /etc/sysconfig/nftables.conf; then
      use_nft=1
      p4=nftables  # 使用nftables替代iptables
    fi

    (
      set -x
      dnf -y -q install $p4 >/dev/null
    ) || exiterr2
  fi
}

# 创建Fail2Ban配置
create_f2b_config() {
  F2B_FILE=/etc/fail2ban/jail.local
  if [ ! -f "$F2B_FILE" ]; then
    bigecho "Creating basic Fail2Ban rules..."
    # 创建基础的SSH保护配置
cat > "$F2B_FILE" <<'EOF'
[ssh-iptables]
enabled = true
filter = sshd
logpath = /var/log/secure
EOF

    # 根据防火墙类型添加相应的封禁动作
    if [ "$use_nft" = 1 ]; then
      # 使用nftables的封禁动作
cat >> "$F2B_FILE" <<'EOF'
port = ssh
banaction = nftables-multiport[blocktype=drop]
EOF
    else
      # 使用iptables的封禁动作
cat >> "$F2B_FILE" <<'EOF'
action = iptables[name=SSH, port=ssh, protocol=tcp]
EOF
    fi
  fi
}

# 安装Fail2Ban（SSH暴力破解防护）
install_fail2ban() {
  bigecho "Installing Fail2Ban to protect SSH..."
  (
    set -x
    # 从EPEL源安装fail2ban
    dnf "$rp1" -y -q install fail2ban >/dev/null
  ) && create_f2b_config  # 安装成功后创建配置
}

# 链接辅助脚本到系统路径
link_scripts() {
  cd /opt/src || exit 1
  # 重命名下载的脚本文件
  /bin/mv -f ikev2setup.sh ikev2.sh
  /bin/mv -f add_vpn_user.sh addvpnuser.sh
  /bin/mv -f del_vpn_user.sh delvpnuser.sh
  echo "+ ikev2.sh addvpnuser.sh delvpnuser.sh"

  # 为每个脚本设置可执行权限并创建符号链接到/usr/bin
  for sc in ikev2.sh addvpnuser.sh delvpnuser.sh; do
    [ -s "$sc" ] && chmod +x "$sc" && ln -s "/opt/src/$sc" /usr/bin 2>/dev/null
  done
}

# 下载VPN管理辅助脚本
get_helper_scripts() {
  bigecho "Downloading helper scripts..."
  # 定义下载源（主源和备用源）
  base1="https://raw.githubusercontent.com/hwdsl2/setup-ipsec-vpn/master/extras"
  base2="https://gitlab.com/hwdsl2/setup-ipsec-vpn/-/raw/master/extras"

  # 定义要下载的脚本文件
  sc1=ikev2setup.sh      # IKEv2设置脚本
  sc2=add_vpn_user.sh    # 添加VPN用户脚本
  sc3=del_vpn_user.sh    # 删除VPN用户脚本

  cd /opt/src || exit 1
  /bin/rm -f "$sc1" "$sc2" "$sc3"  # 删除可能存在的旧文件

  # 尝试从主源下载
  if wget -t 3 -T 30 -q "$base1/$sc1" "$base1/$sc2" "$base1/$sc3"; then
    link_scripts
  else
    # 主源失败，尝试备用源
    /bin/rm -f "$sc1" "$sc2" "$sc3"
    if wget -t 3 -T 30 -q "$base2/$sc1" "$base2/$sc2" "$base2/$sc3"; then
      link_scripts
    else
      echo "Warning: Could not download helper scripts." >&2
      /bin/rm -f "$sc1" "$sc2" "$sc3"
    fi
  fi
}

# =====================================================
# Libreswan IPsec软件的获取和安装
# =====================================================

# 获取Libreswan版本信息
get_swan_ver() {
  SWAN_VER=5.2  # 默认版本

  # 构建版本查询URL
  base_url="https://github.com/hwdsl2/vpn-extras/releases/download/v1.0.0"
  swan_ver_url="$base_url/v1-$os_type-$os_ver-swanver"

  # 尝试获取最新推荐版本
  swan_ver_latest=$(wget -t 2 -T 10 -qO- "$swan_ver_url" | head -n 1)
  [ -z "$swan_ver_latest" ] && swan_ver_latest=$(curl -m 10 -fsL "$swan_ver_url" 2>/dev/null | head -n 1)

  # 验证版本号格式是否正确
  if printf '%s' "$swan_ver_latest" | grep -Eq '^([3-9]|[1-9][0-9]{1,2})(\.([0-9]|[1-9][0-9]{1,2})){1,2}$'; then
    SWAN_VER="$swan_ver_latest"
  fi

  # 如果用户指定了版本，进行验证
  if [ -n "$VPN_SWAN_VER" ]; then
    # 检查用户指定的版本是否在支持范围内
    if ! printf '%s\n%s' "4.15" "$VPN_SWAN_VER" | sort -C -V \
      || ! printf '%s\n%s' "$VPN_SWAN_VER" "$SWAN_VER" | sort -C -V; then
cat 1>&2 <<EOF
Error: Libreswan version '$VPN_SWAN_VER' is not supported.
       This script can install Libreswan 4.15+ or $SWAN_VER.
EOF
      exit 1
    else
      SWAN_VER="$VPN_SWAN_VER"
    fi
  fi
}

# 检查Libreswan是否已安装
check_libreswan() {
  check_result=0

  # 如果IPsec目录不存在，需要安装
  [ ! -d /etc/ipsec.d ] && { get_swan_ver; return 0; }

  # 检查当前安装的版本
  ipsec_ver=$(/usr/local/sbin/ipsec --version 2>/dev/null)
  swan_ver_old=$(printf '%s' "$ipsec_ver" | sed -e 's/.*Libreswan U\?//' -e 's/\( (\|\/K\).*//')
  ipsec_bin="/usr/local/sbin/ipsec"

  # 检查是否为最近安装的有效版本
  if [ -n "$swan_ver_old" ] && printf '%s' "$ipsec_ver" | grep -qi 'libreswan' \
    && [ "$(find "$ipsec_bin" -mmin -10080)" ]; then
    check_result=1  # 标记为已安装
    return 0
  fi

  get_swan_ver

  # 如果版本匹配，更新文件时间戳
  if [ -s "$ipsec_bin" ] && [ "$swan_ver_old" = "$SWAN_VER" ]; then
    touch "$ipsec_bin"
  fi

  # 如果版本匹配，标记为已安装
  [ "$swan_ver_old" = "$SWAN_VER" ] && check_result=1
}

# 下载Libreswan源码
get_libreswan() {
  if [ "$check_result" = 0 ]; then
    bigecho "Downloading Libreswan..."
    cd /opt/src || exit 1

    # 构建下载文件名和URL
    swan_file="libreswan-$SWAN_VER.tar.gz"
    swan_url1="https://github.com/libreswan/libreswan/archive/v$SWAN_VER.tar.gz"
    swan_url2="https://download.libreswan.org/$swan_file"

    (
      set -x
      # 尝试从GitHub下载，失败则从官方站点下载
      wget -t 3 -T 30 -q -O "$swan_file" "$swan_url1" || wget -t 3 -T 30 -q -O "$swan_file" "$swan_url2"
    ) || exit 1

    # 清理旧的源码目录并解压新的源码
    /bin/rm -rf "/opt/src/libreswan-$SWAN_VER"
    tar xzf "$swan_file" && /bin/rm -f "$swan_file"
  else
    bigecho "Libreswan $swan_ver_old is already installed, skipping..."
  fi
}

# 编译和安装Libreswan
install_libreswan() {
  if [ "$check_result" = 0 ]; then
    bigecho "Compiling and installing Libreswan, please wait..."
    cd "libreswan-$SWAN_VER" || exit 1

    # 创建编译配置文件
cat > Makefile.inc.local <<'EOF'
WERROR_CFLAGS=-w -s
USE_DNSSEC=false
USE_DH2=true
USE_NSS_KDF=false
USE_LINUX_AUDIT=false
USE_SECCOMP=false
FINALNSSDIR=/etc/ipsec.d
NSSDIR=/etc/ipsec.d
EOF

    # 检查是否需要特殊的XFRM接口头文件支持
    if ! grep -qs IFLA_XFRM_LINK /usr/include/linux/if_link.h; then
      echo "USE_XFRM_INTERFACE_IFLA_HEADER=true" >> Makefile.inc.local
    fi

    # 获取CPU核心数用于并行编译
    NPROCS=$(grep -c ^processor /proc/cpuinfo)
    [ -z "$NPROCS" ] && NPROCS=1

    (
      set -x
      # 并行编译并安装（使用CPU核心数+1的并行度）
      make "-j$((NPROCS+1))" -s base >/dev/null 2>&1 && make -s install-base >/dev/null 2>&1
    )

    cd /opt/src || exit 1
    # 清理源码目录
    /bin/rm -rf "/opt/src/libreswan-$SWAN_VER"

    # 验证安装是否成功
    if ! /usr/local/sbin/ipsec --version 2>/dev/null | grep -qF "$SWAN_VER" \
      || [ ! -d /etc/ipsec.d ]; then
      exiterr "Libreswan $SWAN_VER failed to build."
    fi
  fi
}

# =====================================================
# VPN配置文件创建
# =====================================================

# 创建VPN配置文件
create_vpn_config() {
  bigecho "Creating VPN configuration..."

  # 设置网络配置参数（使用用户自定义值或默认值）
  L2TP_NET=${VPN_L2TP_NET:-'************/24'}        # L2TP网络段
  L2TP_LOCAL=${VPN_L2TP_LOCAL:-'************'}       # L2TP服务器本地IP
  L2TP_POOL=${VPN_L2TP_POOL:-'************0-**************'}  # L2TP客户端IP池
  XAUTH_NET=${VPN_XAUTH_NET:-'************/24'}      # XAUTH网络段
  XAUTH_POOL=${VPN_XAUTH_POOL:-'*************-**************'}  # XAUTH客户端IP池
  DNS_SRV1=${VPN_DNS_SRV1:-'*******'}               # 主DNS服务器
  DNS_SRV2=${VPN_DNS_SRV2:-'*******'}               # 备用DNS服务器

  # 格式化DNS服务器字符串
  DNS_SRVS="\"$DNS_SRV1 $DNS_SRV2\""
  [ -n "$VPN_DNS_SRV1" ] && [ -z "$VPN_DNS_SRV2" ] && DNS_SRVS="$DNS_SRV1"

  # 创建IPsec主配置文件
  conf_bk "/etc/ipsec.conf"  # 备份原配置文件
cat > /etc/ipsec.conf <<EOF
version 2.0

config setup
  ikev1-policy=accept
  virtual-private=%v4:10.0.0.0/8,%v4:***********/16,%v4:**********/12,%v4:!$L2TP_NET,%v4:!$XAUTH_NET
  uniqueids=no

conn shared
  left=%defaultroute
  leftid=$public_ip
  right=%any
  encapsulation=yes
  authby=secret
  pfs=no
  rekey=no
  keyingtries=5
  dpddelay=30
  dpdtimeout=300
  dpdaction=clear
  ikev2=never
  ike=aes256-sha2;modp2048,aes128-sha2;modp2048,aes256-sha1;modp2048,aes128-sha1;modp2048
  phase2alg=aes_gcm-null,aes128-sha1,aes256-sha1,aes256-sha2_512,aes128-sha2,aes256-sha2
  ikelifetime=24h
  salifetime=24h
  sha2-truncbug=no

conn l2tp-psk
  auto=add
  leftprotoport=17/1701
  rightprotoport=17/%any
  type=transport
  also=shared

conn xauth-psk
  auto=add
  leftsubnet=0.0.0.0/0
  rightaddresspool=$XAUTH_POOL
  modecfgdns=$DNS_SRVS
  leftxauthserver=yes
  rightxauthclient=yes
  leftmodecfgserver=yes
  rightmodecfgclient=yes
  modecfgpull=yes
  cisco-unity=yes
  also=shared

include /etc/ipsec.d/*.conf
EOF

  # 创建IPsec预共享密钥文件
  conf_bk "/etc/ipsec.secrets"
cat > /etc/ipsec.secrets <<EOF
%any  %any  : PSK "$VPN_IPSEC_PSK"
EOF

  # 创建xl2tpd配置文件
  conf_bk "/etc/xl2tpd/xl2tpd.conf"
cat > /etc/xl2tpd/xl2tpd.conf <<EOF
[global]
port = 1701

[lns default]
ip range = $L2TP_POOL
local ip = $L2TP_LOCAL
require chap = yes
refuse pap = yes
require authentication = yes
name = l2tpd
pppoptfile = /etc/ppp/options.xl2tpd
length bit = yes
EOF

  # 创建PPP选项文件
  conf_bk "/etc/ppp/options.xl2tpd"
cat > /etc/ppp/options.xl2tpd <<EOF
+mschap-v2
ipcp-accept-local
ipcp-accept-remote
noccp
auth
mtu 1280
mru 1280
proxyarp
lcp-echo-failure 4
lcp-echo-interval 30
connect-delay 5000
ms-dns $DNS_SRV1
EOF

  # 如果有备用DNS，添加到配置中
  if [ -z "$VPN_DNS_SRV1" ] || [ -n "$VPN_DNS_SRV2" ]; then
cat >> /etc/ppp/options.xl2tpd <<EOF
ms-dns $DNS_SRV2
EOF
  fi

  # 创建VPN用户认证文件（L2TP使用）
  conf_bk "/etc/ppp/chap-secrets"
cat > /etc/ppp/chap-secrets <<EOF
"$VPN_USER" l2tpd "$VPN_PASSWORD" *
EOF

  # 创建XAUTH用户认证文件
  conf_bk "/etc/ipsec.d/passwd"
  VPN_PASSWORD_ENC=$(openssl passwd -1 "$VPN_PASSWORD")  # MD5加密密码
cat > /etc/ipsec.d/passwd <<EOF
$VPN_USER:$VPN_PASSWORD_ENC:xauth-psk
EOF
}

# =====================================================
# 系统配置优化
# =====================================================

# 更新系统内核参数
update_sysctl() {
  bigecho "Updating sysctl settings..."

  # 检查是否已经添加过VPN相关配置
  if ! grep -qs "hwdsl2 VPN script" /etc/sysctl.conf; then
    conf_bk "/etc/sysctl.conf"  # 备份原配置文件

    # 追加VPN优化参数到sysctl.conf
cat >> /etc/sysctl.conf <<EOF

# Added by hwdsl2 VPN script
kernel.msgmnb = 65536
kernel.msgmax = 65536

net.ipv4.ip_forward = 1
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.all.rp_filter = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.conf.default.rp_filter = 0
net.ipv4.conf.$NET_IFACE.send_redirects = 0
net.ipv4.conf.$NET_IFACE.rp_filter = 0

net.core.wmem_max = 16777216
net.core.rmem_max = 16777216
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 87380 16777216
EOF

    # 如果内核支持BBR拥塞控制算法，启用它
    if modprobe -q tcp_bbr \
      && printf '%s\n%s' "4.20" "$(uname -r)" | sort -C -V \
      && [ -f /proc/sys/net/ipv4/tcp_congestion_control ]; then
cat >> /etc/sysctl.conf <<'EOF'
net.core.default_qdisc = fq
net.ipv4.tcp_congestion_control = bbr
EOF
    fi
  fi
}

# =====================================================
# 防火墙配置
# =====================================================

# 更新防火墙规则（iptables或nftables）
update_iptables() {
  bigecho "Updating IPTables rules..."

  # 根据系统类型确定防火墙配置文件路径
  IPT_FILE=/etc/sysconfig/iptables
  [ "$use_nft" = 1 ] && IPT_FILE=/etc/sysconfig/nftables.conf

  ipt_flag=0
  # 检查是否已经配置过VPN防火墙规则
  if ! grep -qs "hwdsl2 VPN script" "$IPT_FILE"; then
    ipt_flag=1
  fi

  # 定义iptables命令别名
  ipi='iptables -I INPUT'      # INPUT链插入规则
  ipf='iptables -I FORWARD'    # FORWARD链插入规则
  ipp='iptables -t nat -I POSTROUTING'  # NAT表POSTROUTING链插入规则
  res='RELATED,ESTABLISHED'    # 连接状态：相关和已建立

  # 定义nftables命令别名
  nff='nft insert rule inet firewalld'
  nfn='nft insert rule inet nftables_svc'

  if [ "$ipt_flag" = 1 ]; then
    # 停止fail2ban服务以避免冲突
    service fail2ban stop >/dev/null 2>&1

    if [ "$use_nft" = 1 ]; then
      # nftables系统的配置
      fd_conf=/etc/firewalld/firewalld.conf
      # 修改firewalld配置，禁用NftablesTableOwner
      if grep -qs '^NftablesTableOwner=yes' "$fd_conf"; then
        sed -i '/NftablesTableOwner/s/yes/no/' "$fd_conf"
        firewall-cmd --reload >/dev/null 2>&1
      fi
      # 备份当前nftables规则
      nft list ruleset > "$IPT_FILE.old-$SYS_DT"
      chmod 600 "$IPT_FILE.old-$SYS_DT"
    else
      # iptables系统的配置
      # 备份当前iptables规则
      iptables-save > "$IPT_FILE.old-$SYS_DT"
    fi

    # 配置INPUT链规则
    $ipi 1 -p udp --dport 1701 -m policy --dir in --pol none -j DROP  # 丢弃未受IPsec保护的L2TP
    $ipi 2 -m conntrack --ctstate INVALID -j DROP                      # 丢弃无效连接
    $ipi 3 -m conntrack --ctstate "$res" -j ACCEPT                     # 允许已建立的连接
    $ipi 4 -p udp -m multiport --dports 500,4500 -j ACCEPT            # 允许IKE和NAT-T端口
    $ipi 5 -p udp --dport 1701 -m policy --dir in --pol ipsec -j ACCEPT  # 允许受IPsec保护的L2TP
    $ipi 6 -p udp --dport 1701 -j DROP                                 # 丢弃其他L2TP流量

    # 配置FORWARD链规则
    $ipf 1 -m conntrack --ctstate INVALID -j DROP                      # 丢弃无效连接
    $ipf 2 -i "$NET_IFACE" -o ppp+ -m conntrack --ctstate "$res" -j ACCEPT  # 允许外网到PPP的已建立连接
    $ipf 3 -i ppp+ -o "$NET_IFACE" -j ACCEPT                          # 允许PPP到外网的流量
    $ipf 4 -i ppp+ -o ppp+ -j ACCEPT                                   # 允许PPP之间的流量
    $ipf 5 -i "$NET_IFACE" -d "$XAUTH_NET" -m conntrack --ctstate "$res" -j ACCEPT  # 允许到XAUTH网段的已建立连接
    $ipf 6 -s "$XAUTH_NET" -o "$NET_IFACE" -j ACCEPT                  # 允许XAUTH网段到外网
    $ipf 7 -s "$XAUTH_NET" -o ppp+ -j ACCEPT                          # 允许XAUTH网段到PPP

    # 对于传统iptables，添加默认DROP规则
    if [ "$use_nft" != 1 ]; then
      iptables -A FORWARD -j DROP
    fi

    # 配置NAT规则
    if [ "$use_nft" = 1 ]; then
      $ipp -s "$XAUTH_NET" -o "$NET_IFACE" ! -d "$XAUTH_NET" -j MASQUERADE
    else
      $ipp -s "$XAUTH_NET" -o "$NET_IFACE" -m policy --dir out --pol none -j MASQUERADE
    fi
    $ipp -s "$L2TP_NET" -o "$NET_IFACE" -j MASQUERADE                  # L2TP网段NAT

    # 保存防火墙规则
    echo "# Modified by hwdsl2 VPN script" > "$IPT_FILE"
    if [ "$use_nft" = 1 ]; then
      # 为nftables添加动态规则
      for vport in 500 4500 1701; do
        $nff filter_INPUT udp dport "$vport" accept 2>/dev/null
        $nfn allow udp dport "$vport" accept 2>/dev/null
      done
      for vnet in "$L2TP_NET" "$XAUTH_NET"; do
        for vdir in saddr daddr; do
          $nff filter_FORWARD ip "$vdir" "$vnet" accept 2>/dev/null
          $nfn FORWARD ip "$vdir" "$vnet" accept 2>/dev/null
        done
      done
      echo "flush ruleset" >> "$IPT_FILE"
      nft list ruleset >> "$IPT_FILE"
    else
      iptables-save >> "$IPT_FILE"
    fi
  fi
}

# 修复NSS加密策略配置
fix_nss_config() {
  nss_conf="/etc/crypto-policies/back-ends/nss.config"
  if [ -s "$nss_conf" ]; then
    # 检查是否需要添加SHA1支持
    if ! grep -q ":SHA1:" "$nss_conf" \
      && ! grep -q " allow=SHA1:" "$nss_conf"; then
      sed -i "/ALL allow=/s/ allow=/ allow=SHA1:/" "$nss_conf"
    fi
  fi
}

# 应用Google Cloud Platform的MTU修复
apply_gcp_mtu_fix() {
  # 检查是否在GCP环境中且MTU为1460
  if dmidecode -s system-product-name 2>/dev/null | grep -qi 'Google Compute Engine' \
    && ifconfig 2>/dev/null | grep "$NET_IFACE" | head -n 1 | grep -qi 'mtu 1460'; then
    bigecho "Applying fix for MTU size..."
    # 设置MTU为1500
    ifconfig "$NET_IFACE" mtu 1500

    # 修改DHCP客户端配置以永久设置MTU
    dh_file="/etc/dhcp/dhclient.conf"
    if grep -qs "send host-name" "$dh_file" \
      && ! grep -qs "interface-mtu 1500" "$dh_file"; then
      sed -i".old-$SYS_DT" \
        "/send host-name/a \\interface \"$NET_IFACE\" {\\ndefault interface-mtu 1500;\\nsupersede interface-mtu 1500;\\n}" \
        "$dh_file"
    fi
  fi
}

# =====================================================
# 服务配置和启动
# =====================================================

# 配置服务开机自启动
enable_on_boot() {
  bigecho "Enabling services on boot..."

  # 屏蔽firewalld服务以避免与iptables/nftables冲突
  systemctl --now mask firewalld 2>/dev/null

  if [ "$use_nft" = 1 ]; then
    # 启用nftables和fail2ban服务
    systemctl enable nftables 2>/dev/null
    systemctl enable fail2ban 2>/dev/null
  else
    # 启用iptables和fail2ban服务
    systemctl enable iptables 2>/dev/null
    systemctl enable fail2ban 2>/dev/null
  fi

  # 配置开机启动脚本
  if ! grep -qs "hwdsl2 VPN script" /etc/rc.local; then
    if [ -f /etc/rc.local ]; then
      conf_bk "/etc/rc.local"  # 备份现有的rc.local
    else
      echo '#!/bin/sh' > /etc/rc.local  # 创建新的rc.local
    fi

    # 添加VPN服务启动脚本
cat >> /etc/rc.local <<'EOF'

# Added by hwdsl2 VPN script
(sleep 15
service ipsec restart
service xl2tpd restart
echo 1 > /proc/sys/net/ipv4/ip_forward)&
EOF
  fi
}

# 启动VPN服务
start_services() {
  bigecho "Starting services..."

  # 应用sysctl配置
  sysctl -e -q -p

  # 设置文件权限
  chmod +x /etc/rc.local
  chmod 600 /etc/ipsec.secrets* /etc/ppp/chap-secrets* /etc/ipsec.d/passwd*

  # 恢复SELinux上下文
  restorecon /etc/ipsec.d/*db 2>/dev/null
  restorecon /usr/local/sbin -Rv 2>/dev/null
  restorecon /usr/local/libexec/ipsec -Rv 2>/dev/null

  # 应用防火墙规则
  if [ "$use_nft" = 1 ]; then
    # 检查nftables配置文件语法
    if ! nft -c -f "$IPT_FILE" >/dev/null 2>&1; then
      # 修复IPv6 MASQUERADE语法问题
      sed -i '/ip6 saddr fddd:\(2c4\|1194\):/s/xt target "MASQUERADE"/masquerade/' "$IPT_FILE"
    fi
    nft -f "$IPT_FILE"
  else
    iptables-restore < "$IPT_FILE"
  fi

  # 修复xl2tpd服务（如果l2tp_ppp模块不可用）
  if ! modprobe -q l2tp_ppp; then
    sed -i '/^ExecStartPre=\//s/=/=-/' /usr/lib/systemd/system/xl2tpd.service
    systemctl daemon-reload
  fi

  # 创建pluto运行目录
  mkdir -p /run/pluto

  # 启动所有VPN相关服务
  service fail2ban restart 2>/dev/null
  service ipsec restart 2>/dev/null
  service xl2tpd restart 2>/dev/null
}

# =====================================================
# 信息显示和IKEv2设置
# =====================================================

# 显示VPN连接信息
show_vpn_info() {
cat <<EOF

================================================

IPsec VPN server is now ready for use!

Connect to your new VPN with these details:

Server IP: $public_ip
IPsec PSK: $VPN_IPSEC_PSK
Username: $VPN_USER
Password: $VPN_PASSWORD

Write these down. You'll need them to connect!

VPN client setup: https://vpnsetup.net/clients

================================================

EOF
}

# 设置IKEv2（如果可用）
set_up_ikev2() {
  status=0

  # 检查是否需要设置IKEv2
  if [ -s /opt/src/ikev2.sh ] && [ ! -f /etc/ipsec.d/ikev2.conf ]; then
    skip_ikev2=0

    # 检查用户是否选择跳过IKEv2设置
    case $VPN_SKIP_IKEV2 in
      [yY][eE][sS])
        skip_ikev2=1
        ;;
    esac

    if [ "$skip_ikev2" = 0 ]; then
      sleep 1
      # 运行IKEv2设置脚本，传递相关环境变量
      VPN_DNS_NAME="$VPN_DNS_NAME" VPN_PUBLIC_IP="$public_ip" \
      VPN_CLIENT_NAME="$VPN_CLIENT_NAME" VPN_XAUTH_POOL="$VPN_XAUTH_POOL" \
      VPN_DNS_SRV1="$VPN_DNS_SRV1" VPN_DNS_SRV2="$VPN_DNS_SRV2" \
      VPN_PROTECT_CONFIG="$VPN_PROTECT_CONFIG" \
      VPN_CLIENT_VALIDITY="$VPN_CLIENT_VALIDITY" \
      /bin/bash /opt/src/ikev2.sh --auto || status=1
    fi
  elif [ -s /opt/src/ikev2.sh ]; then
    # IKEv2已经设置过
cat <<'EOF'
================================================

IKEv2 is already set up on this server.

Next steps: Configure IKEv2 clients. See:
https://vpnsetup.net/clients

To manage IKEv2 clients, run: sudo ikev2.sh

================================================

EOF
  fi
}

# =====================================================
# 主安装流程
# =====================================================

# VPN安装主函数 - 按顺序执行所有安装步骤
vpnsetup() {
  check_root              # 1. 检查root权限
  check_vz                # 2. 检查虚拟化环境
  check_lxc               # 3. 检查LXC容器
  check_os                # 4. 检查操作系统
  check_iface             # 5. 检查网络接口
  check_creds             # 6. 检查VPN凭据
  check_dns               # 7. 检查DNS设置
  check_server_dns        # 8. 检查服务器DNS
  check_client_name       # 9. 检查客户端名称
  check_subnets           # 10. 检查子网设置
  check_libreswan         # 11. 检查Libreswan
  start_setup             # 12. 开始安装设置
  install_setup_pkgs      # 13. 安装基础软件包
  detect_ip               # 14. 检测公网IP
  add_epel_repo           # 15. 添加EPEL源
  install_vpn_pkgs_1      # 16. 安装VPN软件包(第一阶段)
  install_vpn_pkgs_2      # 17. 安装VPN软件包(第二阶段)
  install_vpn_pkgs_3      # 18. 安装VPN软件包(第三阶段)
  install_fail2ban        # 19. 安装Fail2Ban
  get_helper_scripts      # 20. 下载辅助脚本
  get_libreswan           # 21. 下载Libreswan
  install_libreswan       # 22. 编译安装Libreswan
  create_vpn_config       # 23. 创建VPN配置文件
  update_sysctl           # 24. 更新系统参数
  update_iptables         # 25. 配置防火墙规则
  fix_nss_config          # 26. 修复NSS配置
  apply_gcp_mtu_fix       # 27. 应用GCP MTU修复
  enable_on_boot          # 28. 配置开机自启
  start_services          # 29. 启动VPN服务
  show_vpn_info           # 30. 显示连接信息
  set_up_ikev2            # 31. 设置IKEv2（可选）
}

# =====================================================
# 脚本执行入口
# =====================================================

## 延迟执行安装，确保脚本完整加载
vpnsetup "$@"

# 退出并返回状态码
exit "$status"

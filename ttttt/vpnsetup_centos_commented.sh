#!/bin/bash
#
# IPsec VPN服务器自动安装脚本 - CentOS/RHEL/Rocky Linux/AlmaLinux/Oracle Linux版本
# 
# 警告：请勿在个人电脑或Mac上运行此脚本！
#
# 最新版本地址：https://github.com/hwdsl2/setup-ipsec-vpn
#
# <AUTHOR> <EMAIL>
# 基于 Thomas Sarlandie 的工作 (Copyright 2012)
#
# 本作品采用知识共享署名-相同方式共享 3.0 未本地化版本许可协议进行许可
# 许可协议地址：http://creativecommons.org/licenses/by-sa/3.0/
#

# =====================================================
# 用户配置区域 - 请在此处定义您的VPN凭据
# =====================================================

# 定义您自己的IPsec预共享密钥、VPN用户名和密码
# - 所有值必须放在'单引号'内
# - 请勿在值中使用这些特殊字符：\ " '

YOUR_IPSEC_PSK=''    # IPsec预共享密钥（留空则自动生成）
YOUR_USERNAME=''     # VPN用户名（留空则使用默认值vpnuser）
YOUR_PASSWORD=''     # VPN密码（留空则自动生成）

# =====================================================
# 脚本初始化和工具函数定义
# =====================================================

# 设置PATH环境变量，确保能找到所有必要的命令
export PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"

# 生成系统时间戳，用于备份文件命名（格式：YYYY-MM-DD_HH_MM_SS）
SYS_DT=$(date +%F-%T | tr ':' '_')

# 错误处理函数：输出错误信息并退出脚本
exiterr()  { echo "Error: $1" >&2; exit 1; }

# DNF安装失败的专用错误处理函数
exiterr2() { exiterr "'dnf install' failed."; }

# 配置文件备份函数：在修改前创建带时间戳的备份
conf_bk() { /bin/cp -f "$1" "$1.old-$SYS_DT" 2>/dev/null; }

# 大标题输出函数：用于显示安装步骤
bigecho() { echo "## $1"; }

# =====================================================
# 验证和检查函数
# =====================================================

# 检查IP地址格式是否有效
check_ip() {
  # IPv4地址的正则表达式模式
  IP_REGEX='^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$'
  printf '%s' "$1" | tr -d '\n' | grep -Eq "$IP_REGEX"
}

# 检查DNS域名格式是否有效（FQDN格式）
check_dns_name() {
  # 完全限定域名的正则表达式模式
  FQDN_REGEX='^([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$'
  printf '%s' "$1" | tr -d '\n' | grep -Eq "$FQDN_REGEX"
}

# 检查脚本是否以root权限运行
check_root() {
  if [ "$(id -u)" != 0 ]; then
    exiterr "Script must be run as root. Try 'sudo bash $0'"
  fi
}

# 检查是否在OpenVZ虚拟化环境中运行（不支持）
check_vz() {
  if [ -f /proc/user_beancounters ]; then
    exiterr "OpenVZ VPS is not supported."
  fi
}

# 检查LXC容器环境是否正确配置
check_lxc() {
  # shellcheck disable=SC2154
  if [ "$container" = "lxc" ] && [ ! -e /dev/ppp ]; then
cat 1>&2 <<'EOF'
Error: /dev/ppp is missing. LXC containers require configuration.
       See: https://github.com/hwdsl2/setup-ipsec-vpn/issues/1014
EOF
  exit 1
  fi
}

# 检查操作系统类型和版本
check_os() {
  rh_file="/etc/redhat-release"
  if [ -f "$rh_file" ]; then
    # 默认设置为CentOS
    os_type=centos
    
    # 检查具体的发行版类型
    if grep -q "Red Hat" "$rh_file"; then
      os_type=rhel
    fi
    [ -f /etc/oracle-release ] && os_type=ol
    grep -qi rocky "$rh_file" && os_type=rocky
    grep -qi alma "$rh_file" && os_type=alma
    
    # 检查版本号
    if grep -q "release 7" "$rh_file"; then
      os_ver=7
    elif grep -q "release 8" "$rh_file"; then
      os_ver=8
      grep -qi stream "$rh_file" && os_ver=8s  # CentOS Stream 8
    elif grep -q "release 9" "$rh_file"; then
      os_ver=9
      grep -qi stream "$rh_file" && os_ver=9s  # CentOS Stream 9
    elif grep -q "release 10" "$rh_file"; then
      os_ver=10
      grep -qi stream "$rh_file" && os_ver=10s  # CentOS Stream 10
    else
      exiterr "This script only supports CentOS/RHEL 7-10."
    fi
    
    # 检查是否为已停止支持的CentOS版本
    if [ "$os_type" = "centos" ] \
      && { [ "$os_ver" = 7 ] || [ "$os_ver" = 8 ] || [ "$os_ver" = 8s ]; }; then
      exiterr "CentOS Linux $os_ver is EOL and not supported."
    fi
  else
cat 1>&2 <<'EOF'
Error: This script only supports one of the following OS:
       CentOS/RHEL, Rocky Linux, AlmaLinux or Oracle Linux
EOF
    exit 1
  fi
}

# 检查和确定默认网络接口
check_iface() {
  # 如果系统没有route或ip命令，先安装iproute包
  if ! command -v route >/dev/null 2>&1 && ! command -v ip >/dev/null 2>&1; then
    (
      set -x
      dnf -y -q install iproute >/dev/null || dnf -y -q install iproute >/dev/null
    )
  fi
  
  # 尝试获取默认路由的网络接口
  def_iface=$(route 2>/dev/null | grep -m 1 '^default' | grep -o '[^ ]*$')
  [ -z "$def_iface" ] && def_iface=$(ip -4 route list 0/0 2>/dev/null | grep -m 1 -Po '(?<=dev )(\S+)')
  
  # 检查接口状态
  def_state=$(cat "/sys/class/net/$def_iface/operstate" 2>/dev/null)
  if [ -n "$def_state" ] && [ "$def_state" != "down" ]; then
    case $def_iface in
      wl*)
        # 检测到无线接口，警告用户不要在个人电脑上运行
        exiterr "Wireless interface '$def_iface' detected. DO NOT run this script on your PC or Mac!"
        ;;
    esac
    NET_IFACE="$def_iface"
  else
    # 如果默认接口不可用，尝试使用eth0
    eth0_state=$(cat "/sys/class/net/eth0/operstate" 2>/dev/null)
    if [ -z "$eth0_state" ] || [ "$eth0_state" = "down" ]; then
      exiterr "Could not detect the default network interface."
    fi
    NET_IFACE=eth0
  fi
}

# 检查和设置VPN凭据
check_creds() {
  # 如果用户设置了凭据，则使用用户设置的值
  [ -n "$YOUR_IPSEC_PSK" ] && VPN_IPSEC_PSK="$YOUR_IPSEC_PSK"
  [ -n "$YOUR_USERNAME" ] && VPN_USER="$YOUR_USERNAME"
  [ -n "$YOUR_PASSWORD" ] && VPN_PASSWORD="$YOUR_PASSWORD"
  
  # 如果用户没有设置任何凭据，则自动生成
  if [ -z "$VPN_IPSEC_PSK" ] && [ -z "$VPN_USER" ] && [ -z "$VPN_PASSWORD" ]; then
    bigecho "VPN credentials not set by user. Generating random PSK and password..."
    # 生成20字符的随机PSK（排除容易混淆的字符）
    VPN_IPSEC_PSK=$(LC_CTYPE=C tr -dc 'A-HJ-NPR-Za-km-z2-9' </dev/urandom 2>/dev/null | head -c 20)
    VPN_USER=vpnuser
    # 生成16字符的随机密码
    VPN_PASSWORD=$(LC_CTYPE=C tr -dc 'A-HJ-NPR-Za-km-z2-9' </dev/urandom 2>/dev/null | head -c 16)
  fi
  
  # 验证所有凭据都已设置
  if [ -z "$VPN_IPSEC_PSK" ] || [ -z "$VPN_USER" ] || [ -z "$VPN_PASSWORD" ]; then
    exiterr "All VPN credentials must be specified. Edit the script and re-enter them."
  fi
  
  # 检查凭据是否包含非ASCII字符
  if printf '%s' "$VPN_IPSEC_PSK $VPN_USER $VPN_PASSWORD" | LC_ALL=C grep -q '[^ -~]\+'; then
    exiterr "VPN credentials must not contain non-ASCII characters."
  fi
  
  # 检查凭据是否包含特殊字符
  case "$VPN_IPSEC_PSK $VPN_USER $VPN_PASSWORD" in
    *[\\\"\']*)
      exiterr "VPN credentials must not contain these special characters: \\ \" '"
      ;;
  esac
}

# 检查DNS服务器设置
check_dns() {
  if { [ -n "$VPN_DNS_SRV1" ] && ! check_ip "$VPN_DNS_SRV1"; } \
    || { [ -n "$VPN_DNS_SRV2" ] && ! check_ip "$VPN_DNS_SRV2"; }; then
    exiterr "The DNS server specified is invalid."
  fi
}

# 检查服务器DNS名称设置
check_server_dns() {
  if [ -n "$VPN_DNS_NAME" ] && ! check_dns_name "$VPN_DNS_NAME"; then
    exiterr "Invalid DNS name. 'VPN_DNS_NAME' must be a fully qualified domain name (FQDN)."
  fi
}

# 检查客户端名称设置
check_client_name() {
  if [ -n "$VPN_CLIENT_NAME" ]; then
    name_len="$(printf '%s' "$VPN_CLIENT_NAME" | wc -m)"
    # 验证客户端名称长度和字符
    if [ "$name_len" -gt "64" ] || printf '%s' "$VPN_CLIENT_NAME" | LC_ALL=C grep -q '[^A-Za-z0-9_-]\+' \
      || case $VPN_CLIENT_NAME in -*) true ;; *) false ;; esac; then
      exiterr "Invalid client name. Use one word only, no special characters except '-' and '_'."
    fi
  fi
}

# 检查VPN子网设置（用于升级场景）
check_subnets() {
  if [ -s /etc/ipsec.conf ] && grep -qs "hwdsl2 VPN script" /etc/sysctl.conf; then
    # 如果是升级安装，检查自定义子网是否与初始安装匹配
    L2TP_NET=${VPN_L2TP_NET:-'************/24'}
    XAUTH_NET=${VPN_XAUTH_NET:-'************/24'}
    if ! grep -q "$L2TP_NET" /etc/ipsec.conf \
      || ! grep -q "$XAUTH_NET" /etc/ipsec.conf; then
      echo "Error: The custom VPN subnets specified do not match initial install." >&2
      echo "       See Advanced usage -> Customize VPN subnets for more information." >&2
      exit 1
    fi
  fi
}

# =====================================================
# 安装准备和软件包安装函数
# =====================================================

# 开始安装设置
start_setup() {
  bigecho "VPN setup in progress... Please be patient."
  # 创建源码目录并切换到该目录
  mkdir -p /opt/src
  cd /opt/src || exit 1
}

# 安装设置所需的基础软件包
install_setup_pkgs() {
  bigecho "Installing packages required for setup..."
  (
    set -x
    # 安装基础工具包：wget(下载)、bind-utils(DNS工具)、openssl(加密)、tar(解压)
    # iptables(防火墙)、iproute(网络)、gawk/grep/sed(文本处理)、net-tools(网络工具)
    dnf -y -q install wget bind-utils openssl tar \
      iptables iproute gawk grep sed net-tools >/dev/null \
    || dnf -y -q install wget bind-utils openssl tar \
      iptables iproute gawk grep sed net-tools >/dev/null
  ) || exiterr2
}

# 获取服务器的默认IP地址
get_default_ip() {
  # 使用ip命令获取默认路由的源IP
  def_ip=$(ip -4 route get 1 | sed 's/ uid .*//' | awk '{print $NF;exit}' 2>/dev/null)
  # 检查是否为公网IP（排除私有IP段）
  if check_ip "$def_ip" \
    && ! printf '%s' "$def_ip" | grep -Eq '^(10|127|172\.(1[6-9]|2[0-9]|3[0-1])|192\.168|169\.254)\.'; then
    public_ip="$def_ip"
  fi
}

# 检测服务器的公网IP地址
detect_ip() {
  # 首先尝试使用用户指定的IP或获取默认IP
  public_ip=${VPN_PUBLIC_IP:-''}
  check_ip "$public_ip" || get_default_ip
  check_ip "$public_ip" && return 0
  
  bigecho "Trying to auto discover IP of this server..."
  # 尝试通过外部服务获取公网IP
  check_ip "$public_ip" || public_ip=$(dig @resolver1.opendns.com -t A -4 myip.opendns.com +short)
  check_ip "$public_ip" || public_ip=$(wget -t 2 -T 10 -qO- http://ipv4.icanhazip.com)
  check_ip "$public_ip" || public_ip=$(wget -t 2 -T 10 -qO- http://ip1.dynupdate.no-ip.com)
  check_ip "$public_ip" || exiterr "Cannot detect this server's public IP. Define it as variable 'VPN_PUBLIC_IP' and re-run this script."
}

# 添加EPEL软件源
add_epel_repo() {
  bigecho "Adding the EPEL repository..."
  # 构建EPEL源的下载URL
  epel_url="https://dl.fedoraproject.org/pub/epel/epel-release-latest-$(rpm -E '%{rhel}').noarch.rpm"
  (
    set -x
    # 尝试安装EPEL源
    dnf -y -q install epel-release >/dev/null 2>&1 || dnf -y -q install "$epel_url" >/dev/null
  ) || exiterr2
}

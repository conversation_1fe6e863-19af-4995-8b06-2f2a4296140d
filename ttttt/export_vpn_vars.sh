#!/bin/bash
#
# VPN变量导出脚本
# 将所有VPN相关变量导出为可重用的格式
#

# 输出文件
OUTPUT_FILE="vpn_variables_export.txt"
ENV_FILE="vpn_environment.env"

# 检查权限
if [ "$(id -u)" != 0 ]; then
    echo "警告: 建议以root权限运行以获取完整信息"
fi

echo "正在导出VPN变量..."

# 创建导出文件
cat > "$OUTPUT_FILE" <<EOF
# VPN变量导出文件
# 生成时间: $(date)
# 生成主机: $(hostname)
# 操作系统: $(cat /etc/redhat-release 2>/dev/null || echo '未知')

EOF

# 创建环境变量文件
cat > "$ENV_FILE" <<EOF
# VPN环境变量文件
# 可以通过 source vpn_environment.env 加载这些变量
# 生成时间: $(date)

EOF

# 函数：添加变量到文件
add_variable() {
    local var_name="$1"
    local var_value="$2"
    local var_desc="$3"
    local source="$4"
    
    # 添加到详细文件
    echo "# $var_desc" >> "$OUTPUT_FILE"
    echo "# 来源: $source" >> "$OUTPUT_FILE"
    echo "$var_name=\"$var_value\"" >> "$OUTPUT_FILE"
    echo "" >> "$OUTPUT_FILE"
    
    # 添加到环境变量文件（如果有值）
    if [ -n "$var_value" ]; then
        echo "export $var_name=\"$var_value\"" >> "$ENV_FILE"
    fi
}

# 1. 系统检测变量
echo "正在收集系统信息..."

# 时间戳
SYS_DT=$(date +%F-%T | tr ':' '_')
add_variable "SYS_DT" "$SYS_DT" "系统时间戳，用于备份文件命名" "系统生成"

# 网络接口
if command -v ip >/dev/null 2>&1; then
    NET_IFACE=$(ip -4 route list 0/0 2>/dev/null | grep -m 1 -Po '(?<=dev )(\S+)')
elif command -v route >/dev/null 2>&1; then
    NET_IFACE=$(route 2>/dev/null | grep -m 1 '^default' | grep -o '[^ ]*$')
fi
[ -z "$NET_IFACE" ] && NET_IFACE="eth0"
add_variable "NET_IFACE" "$NET_IFACE" "默认网络接口名" "系统检测"

# 公网IP
if command -v wget >/dev/null 2>&1; then
    public_ip=$(wget -t 2 -T 10 -qO- http://ipv4.icanhazip.com 2>/dev/null)
elif command -v curl >/dev/null 2>&1; then
    public_ip=$(curl -s --max-time 10 http://ipv4.icanhazip.com 2>/dev/null)
fi
add_variable "public_ip" "$public_ip" "服务器公网IP地址" "外部服务检测"

# 操作系统信息
if [ -f "/etc/redhat-release" ]; then
    rh_file="/etc/redhat-release"
    os_type=centos
    if grep -q "Red Hat" "$rh_file"; then
        os_type=rhel
    fi
    [ -f /etc/oracle-release ] && os_type=ol
    grep -qi rocky "$rh_file" && os_type=rocky
    grep -qi alma "$rh_file" && os_type=alma
    
    if grep -q "release 7" "$rh_file"; then
        os_ver=7
    elif grep -q "release 8" "$rh_file"; then
        os_ver=8
    elif grep -q "release 9" "$rh_file"; then
        os_ver=9
    elif grep -q "release 10" "$rh_file"; then
        os_ver=10
    fi
fi
add_variable "os_type" "$os_type" "操作系统类型" "系统检测"
add_variable "os_ver" "$os_ver" "操作系统版本" "系统检测"

# 防火墙类型
use_nft=0
if [ "$os_ver" = 9 ] || [ "$os_ver" = 10 ] || systemctl is-active --quiet firewalld 2>/dev/null; then
    use_nft=1
fi
add_variable "use_nft" "$use_nft" "是否使用nftables (0=iptables, 1=nftables)" "系统检测"

# 2. 从配置文件读取变量
echo "正在读取配置文件..."

# IPsec配置
if [ -f "/etc/ipsec.secrets" ] && [ -r "/etc/ipsec.secrets" ]; then
    VPN_IPSEC_PSK=$(grep "PSK" /etc/ipsec.secrets 2>/dev/null | awk -F'"' '{print $2}' | head -1)
    add_variable "VPN_IPSEC_PSK" "$VPN_IPSEC_PSK" "IPsec预共享密钥" "/etc/ipsec.secrets"
fi

# 用户认证
if [ -f "/etc/ppp/chap-secrets" ] && [ -r "/etc/ppp/chap-secrets" ]; then
    user_line=$(grep -v "^#" /etc/ppp/chap-secrets 2>/dev/null | grep -v "^$" | head -1)
    if [ -n "$user_line" ]; then
        VPN_USER=$(echo "$user_line" | awk '{print $1}' | tr -d '"')
        VPN_PASSWORD=$(echo "$user_line" | awk '{print $3}' | tr -d '"')
        add_variable "VPN_USER" "$VPN_USER" "VPN用户名" "/etc/ppp/chap-secrets"
        add_variable "VPN_PASSWORD" "$VPN_PASSWORD" "VPN密码" "/etc/ppp/chap-secrets"
    fi
fi

# L2TP网络配置
if [ -f "/etc/xl2tpd/xl2tpd.conf" ]; then
    L2TP_POOL=$(grep "ip range" /etc/xl2tpd/xl2tpd.conf 2>/dev/null | awk -F'=' '{print $2}' | tr -d ' ')
    L2TP_LOCAL=$(grep "local ip" /etc/xl2tpd/xl2tpd.conf 2>/dev/null | awk -F'=' '{print $2}' | tr -d ' ')
    add_variable "VPN_L2TP_POOL" "$L2TP_POOL" "L2TP客户端IP池" "/etc/xl2tpd/xl2tpd.conf"
    add_variable "VPN_L2TP_LOCAL" "$L2TP_LOCAL" "L2TP服务器本地IP" "/etc/xl2tpd/xl2tpd.conf"
    
    # 推断网络段
    if [ -n "$L2TP_POOL" ]; then
        first_ip=$(echo "$L2TP_POOL" | cut -d'-' -f1)
        if [[ "$first_ip" =~ ^192\.168\.([0-9]+)\. ]]; then
            subnet=$(echo "$first_ip" | cut -d'.' -f3)
            VPN_L2TP_NET="192.168.$subnet.0/24"
            add_variable "VPN_L2TP_NET" "$VPN_L2TP_NET" "L2TP网络段" "从IP池推断"
        fi
    fi
fi

# DNS配置
if [ -f "/etc/ppp/options.xl2tpd" ]; then
    VPN_DNS_SRV1=$(grep "ms-dns" /etc/ppp/options.xl2tpd 2>/dev/null | head -1 | awk '{print $2}')
    VPN_DNS_SRV2=$(grep "ms-dns" /etc/ppp/options.xl2tpd 2>/dev/null | tail -1 | awk '{print $2}')
    [ "$VPN_DNS_SRV1" = "$VPN_DNS_SRV2" ] && VPN_DNS_SRV2=""
    add_variable "VPN_DNS_SRV1" "$VPN_DNS_SRV1" "主DNS服务器" "/etc/ppp/options.xl2tpd"
    [ -n "$VPN_DNS_SRV2" ] && add_variable "VPN_DNS_SRV2" "$VPN_DNS_SRV2" "备用DNS服务器" "/etc/ppp/options.xl2tpd"
fi

# XAUTH配置（如果存在）
if [ -f "/etc/ipsec.conf" ]; then
    XAUTH_POOL=$(grep "rightaddresspool" /etc/ipsec.conf 2>/dev/null | awk -F'=' '{print $2}' | tr -d ' ')
    if [ -n "$XAUTH_POOL" ]; then
        add_variable "VPN_XAUTH_POOL" "$XAUTH_POOL" "XAUTH客户端IP池" "/etc/ipsec.conf"
        
        # 推断XAUTH网络段
        first_ip=$(echo "$XAUTH_POOL" | cut -d'-' -f1)
        if [[ "$first_ip" =~ ^192\.168\.([0-9]+)\. ]]; then
            subnet=$(echo "$first_ip" | cut -d'.' -f3)
            VPN_XAUTH_NET="192.168.$subnet.0/24"
            add_variable "VPN_XAUTH_NET" "$VPN_XAUTH_NET" "XAUTH网络段" "从IP池推断"
        fi
    fi
fi

# Libreswan版本
SWAN_VER="5.2"
if command -v /usr/local/sbin/ipsec >/dev/null 2>&1; then
    ipsec_ver=$(/usr/local/sbin/ipsec --version 2>/dev/null)
    if [ -n "$ipsec_ver" ]; then
        swan_ver_current=$(echo "$ipsec_ver" | sed -e 's/.*Libreswan U\?//' -e 's/\( (\|\/K\).*//')
        [ -n "$swan_ver_current" ] && SWAN_VER="$swan_ver_current"
    fi
fi
add_variable "SWAN_VER" "$SWAN_VER" "Libreswan版本" "程序检测"

# 3. 添加默认值变量
echo "正在添加默认配置..."

# 如果没有从配置文件读取到，使用默认值
[ -z "$VPN_L2TP_NET" ] && add_variable "VPN_L2TP_NET" "************/24" "L2TP网络段(默认)" "默认值"
[ -z "$VPN_L2TP_LOCAL" ] && add_variable "VPN_L2TP_LOCAL" "************" "L2TP服务器本地IP(默认)" "默认值"
[ -z "$VPN_L2TP_POOL" ] && add_variable "VPN_L2TP_POOL" "*************-**************" "L2TP客户端IP池(默认)" "默认值"
[ -z "$VPN_XAUTH_NET" ] && add_variable "VPN_XAUTH_NET" "************/24" "XAUTH网络段(默认)" "默认值"
[ -z "$VPN_XAUTH_POOL" ] && add_variable "VPN_XAUTH_POOL" "*************-**************" "XAUTH客户端IP池(默认)" "默认值"
[ -z "$VPN_DNS_SRV1" ] && add_variable "VPN_DNS_SRV1" "*******" "主DNS服务器(默认)" "默认值"
[ -z "$VPN_DNS_SRV2" ] && add_variable "VPN_DNS_SRV2" "*******" "备用DNS服务器(默认)" "默认值"

# 4. 添加路径变量
echo "正在添加路径信息..."

add_variable "PATH" "$PATH" "系统PATH环境变量" "系统环境"

# 防火墙配置文件路径
if [ "$use_nft" = 1 ]; then
    IPT_FILE="/etc/sysconfig/nftables.conf"
else
    IPT_FILE="/etc/sysconfig/iptables"
fi
add_variable "IPT_FILE" "$IPT_FILE" "防火墙配置文件路径" "根据系统选择"

# 5. 添加服务状态
echo "正在检查服务状态..."

services=("ipsec" "xl2tpd" "fail2ban" "firewalld" "nftables" "iptables")
for service in "${services[@]}"; do
    if systemctl is-active --quiet "$service" 2>/dev/null; then
        status="active"
    else
        status="inactive"
    fi
    
    if systemctl is-enabled --quiet "$service" 2>/dev/null; then
        enabled="enabled"
    else
        enabled="disabled"
    fi
    
    add_variable "${service}_status" "$status" "$service 服务运行状态" "systemctl检测"
    add_variable "${service}_enabled" "$enabled" "$service 服务启用状态" "systemctl检测"
done

# 6. 添加网络状态
echo "正在检查网络状态..."

# IP转发状态
ip_forward=$(cat /proc/sys/net/ipv4/ip_forward 2>/dev/null)
add_variable "ip_forward" "$ip_forward" "IP转发状态 (1=启用, 0=禁用)" "/proc/sys/net/ipv4/ip_forward"

# 端口监听状态
if command -v ss >/dev/null 2>&1; then
    for port in 500 4500 1701; do
        if ss -ulnp 2>/dev/null | grep -q ":$port "; then
            listening="yes"
        else
            listening="no"
        fi
        add_variable "port_${port}_listening" "$listening" "UDP端口$port监听状态" "ss命令检测"
    done
fi

# 完成环境变量文件
echo "" >> "$ENV_FILE"
echo "# 使用方法:" >> "$ENV_FILE"
echo "# source vpn_environment.env" >> "$ENV_FILE"
echo "# 然后可以使用 \$VPN_IPSEC_PSK 等变量" >> "$ENV_FILE"

# 创建JSON格式导出
JSON_FILE="vpn_variables.json"
echo "正在生成JSON格式..."

cat > "$JSON_FILE" <<EOF
{
  "export_info": {
    "timestamp": "$(date -Iseconds)",
    "hostname": "$(hostname)",
    "os": "$(cat /etc/redhat-release 2>/dev/null || echo 'unknown')",
    "kernel": "$(uname -r)"
  },
  "vpn_credentials": {
    "ipsec_psk": "$VPN_IPSEC_PSK",
    "username": "$VPN_USER",
    "password": "$VPN_PASSWORD"
  },
  "network_config": {
    "public_ip": "$public_ip",
    "network_interface": "$NET_IFACE",
    "l2tp_net": "$VPN_L2TP_NET",
    "l2tp_local": "$VPN_L2TP_LOCAL",
    "l2tp_pool": "$VPN_L2TP_POOL",
    "xauth_net": "$VPN_XAUTH_NET",
    "xauth_pool": "$VPN_XAUTH_POOL",
    "dns_primary": "$VPN_DNS_SRV1",
    "dns_secondary": "$VPN_DNS_SRV2"
  },
  "system_info": {
    "os_type": "$os_type",
    "os_version": "$os_ver",
    "libreswan_version": "$SWAN_VER",
    "use_nftables": $use_nft,
    "ip_forward_enabled": $([ "$ip_forward" = "1" ] && echo "true" || echo "false")
  }
}
EOF

echo "导出完成！"
echo ""
echo "生成的文件："
echo "  $OUTPUT_FILE - 详细变量列表（带注释）"
echo "  $ENV_FILE - 环境变量文件（可source加载）"
echo "  $JSON_FILE - JSON格式数据"
echo ""
echo "使用方法："
echo "  查看详细信息: cat $OUTPUT_FILE"
echo "  加载环境变量: source $ENV_FILE"
echo "  解析JSON数据: cat $JSON_FILE | jq ."
echo ""
echo "文件大小："
ls -lh "$OUTPUT_FILE" "$ENV_FILE" "$JSON_FILE" 2>/dev/null | awk '{print "  " $9 ": " $5}'

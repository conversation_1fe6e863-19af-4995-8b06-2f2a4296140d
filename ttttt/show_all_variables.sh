#!/bin/bash
#
# VPN变量显示脚本
# 一次性输出所有VPN相关的变量内容
#

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 输出函数
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================================${NC}"
}

print_section() {
    echo -e "${CYAN}--- $1 ---${NC}"
}

print_variable() {
    local var_name="$1"
    local var_value="$2"
    local var_desc="$3"
    local default_value="$4"
    
    if [ -n "$var_value" ]; then
        echo -e "${GREEN}$var_name${NC} = ${YELLOW}$var_value${NC}"
    elif [ -n "$default_value" ]; then
        echo -e "${GREEN}$var_name${NC} = ${PURPLE}$default_value${NC} ${CYAN}(默认值)${NC}"
    else
        echo -e "${GREEN}$var_name${NC} = ${RED}(未设置)${NC}"
    fi
    
    if [ -n "$var_desc" ]; then
        echo -e "  ${CYAN}说明: $var_desc${NC}"
    fi
    echo
}

# 检查是否以root权限运行
check_root() {
    if [ "$(id -u)" != 0 ]; then
        echo -e "${RED}警告: 建议以root权限运行以获取完整信息${NC}"
        echo -e "${YELLOW}某些配置文件可能无法读取${NC}"
        echo
    fi
}

# 模拟脚本环境变量设置
simulate_script_environment() {
    # 设置PATH
    export PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
    
    # 生成时间戳
    SYS_DT=$(date +%F-%T | tr ':' '_')
    
    # 检测操作系统
    if [ -f "/etc/redhat-release" ]; then
        rh_file="/etc/redhat-release"
        os_type=centos
        if grep -q "Red Hat" "$rh_file"; then
            os_type=rhel
        fi
        [ -f /etc/oracle-release ] && os_type=ol
        grep -qi rocky "$rh_file" && os_type=rocky
        grep -qi alma "$rh_file" && os_type=alma
        
        if grep -q "release 7" "$rh_file"; then
            os_ver=7
        elif grep -q "release 8" "$rh_file"; then
            os_ver=8
        elif grep -q "release 9" "$rh_file"; then
            os_ver=9
        elif grep -q "release 10" "$rh_file"; then
            os_ver=10
        else
            os_ver="unknown"
        fi
    else
        os_type="unknown"
        os_ver="unknown"
    fi
    
    # 检测网络接口
    if command -v route >/dev/null 2>&1; then
        def_iface=$(route 2>/dev/null | grep -m 1 '^default' | grep -o '[^ ]*$')
    fi
    if [ -z "$def_iface" ] && command -v ip >/dev/null 2>&1; then
        def_iface=$(ip -4 route list 0/0 2>/dev/null | grep -m 1 -Po '(?<=dev )(\S+)')
    fi
    [ -n "$def_iface" ] && NET_IFACE="$def_iface" || NET_IFACE="eth0"
    
    # 检测公网IP
    if command -v wget >/dev/null 2>&1; then
        public_ip=$(wget -t 2 -T 10 -qO- http://ipv4.icanhazip.com 2>/dev/null)
    elif command -v curl >/dev/null 2>&1; then
        public_ip=$(curl -s --max-time 10 http://ipv4.icanhazip.com 2>/dev/null)
    fi
    [ -z "$public_ip" ] && public_ip="<无法检测>"
    
    # 检测是否使用nftables
    use_nft=0
    if [ "$os_ver" = 9 ] || [ "$os_ver" = 10 ] || systemctl is-active --quiet firewalld 2>/dev/null; then
        use_nft=1
    fi
    
    # 设置Libreswan版本
    SWAN_VER=5.2
    if command -v /usr/local/sbin/ipsec >/dev/null 2>&1; then
        ipsec_ver=$(/usr/local/sbin/ipsec --version 2>/dev/null)
        if [ -n "$ipsec_ver" ]; then
            swan_ver_current=$(echo "$ipsec_ver" | sed -e 's/.*Libreswan U\?//' -e 's/\( (\|\/K\).*//')
            [ -n "$swan_ver_current" ] && SWAN_VER="$swan_ver_current"
        fi
    fi
}

# 从配置文件读取变量
read_config_variables() {
    # 从IPsec配置读取
    if [ -f "/etc/ipsec.secrets" ]; then
        VPN_IPSEC_PSK=$(grep "PSK" /etc/ipsec.secrets 2>/dev/null | awk -F'"' '{print $2}' | head -1)
    fi
    
    # 从用户认证文件读取
    if [ -f "/etc/ppp/chap-secrets" ]; then
        local user_line=$(grep -v "^#" /etc/ppp/chap-secrets 2>/dev/null | grep -v "^$" | head -1)
        if [ -n "$user_line" ]; then
            VPN_USER=$(echo "$user_line" | awk '{print $1}' | tr -d '"')
            VPN_PASSWORD=$(echo "$user_line" | awk '{print $3}' | tr -d '"')
        fi
    fi
    
    # 从xl2tpd配置读取网络设置
    if [ -f "/etc/xl2tpd/xl2tpd.conf" ]; then
        L2TP_POOL=$(grep "ip range" /etc/xl2tpd/xl2tpd.conf 2>/dev/null | awk -F'=' '{print $2}' | tr -d ' ')
        L2TP_LOCAL=$(grep "local ip" /etc/xl2tpd/xl2tpd.conf 2>/dev/null | awk -F'=' '{print $2}' | tr -d ' ')
        
        # 从IP池推断网络段
        if [ -n "$L2TP_POOL" ]; then
            local first_ip=$(echo "$L2TP_POOL" | cut -d'-' -f1)
            if [[ "$first_ip" =~ ^192\.168\.42\. ]]; then
                L2TP_NET="************/24"
            elif [[ "$first_ip" =~ ^192\.168\.([0-9]+)\. ]]; then
                local subnet=$(echo "$first_ip" | cut -d'.' -f3)
                L2TP_NET="192.168.$subnet.0/24"
            fi
        fi
    fi
    
    # 从PPP配置读取DNS设置
    if [ -f "/etc/ppp/options.xl2tpd" ]; then
        DNS_SRV1=$(grep "ms-dns" /etc/ppp/options.xl2tpd 2>/dev/null | head -1 | awk '{print $2}')
        DNS_SRV2=$(grep "ms-dns" /etc/ppp/options.xl2tpd 2>/dev/null | tail -1 | awk '{print $2}')
        [ "$DNS_SRV1" = "$DNS_SRV2" ] && DNS_SRV2=""
    fi
    
    # 从IPsec配置读取XAUTH网络（如果存在）
    if [ -f "/etc/ipsec.conf" ]; then
        XAUTH_POOL=$(grep "rightaddresspool" /etc/ipsec.conf 2>/dev/null | awk -F'=' '{print $2}' | tr -d ' ')
        if [ -n "$XAUTH_POOL" ]; then
            local first_ip=$(echo "$XAUTH_POOL" | cut -d'-' -f1)
            if [[ "$first_ip" =~ ^192\.168\.43\. ]]; then
                XAUTH_NET="************/24"
            elif [[ "$first_ip" =~ ^192\.168\.([0-9]+)\. ]]; then
                local subnet=$(echo "$first_ip" | cut -d'.' -f3)
                XAUTH_NET="192.168.$subnet.0/24"
            fi
        fi
    fi
}

# 显示用户可配置变量
show_user_configurable_variables() {
    print_section "用户可配置的环境变量"
    
    print_variable "YOUR_IPSEC_PSK" "$YOUR_IPSEC_PSK" "用户设置的IPsec预共享密钥" ""
    print_variable "YOUR_USERNAME" "$YOUR_USERNAME" "用户设置的VPN用户名" ""
    print_variable "YOUR_PASSWORD" "$YOUR_PASSWORD" "用户设置的VPN密码" ""
    
    print_variable "VPN_IPSEC_PSK" "$VPN_IPSEC_PSK" "实际使用的IPsec预共享密钥" "随机生成"
    print_variable "VPN_USER" "$VPN_USER" "实际使用的VPN用户名" "vpnuser"
    print_variable "VPN_PASSWORD" "$VPN_PASSWORD" "实际使用的VPN密码" "随机生成"
    
    print_variable "VPN_PUBLIC_IP" "$VPN_PUBLIC_IP" "服务器公网IP地址" "$public_ip"
    print_variable "VPN_DNS_SRV1" "$VPN_DNS_SRV1" "主DNS服务器" "${DNS_SRV1:-*******}"
    print_variable "VPN_DNS_SRV2" "$VPN_DNS_SRV2" "备用DNS服务器" "${DNS_SRV2:-*******}"
    print_variable "VPN_DNS_NAME" "$VPN_DNS_NAME" "服务器DNS名称(FQDN)" ""
    
    print_variable "VPN_L2TP_NET" "$VPN_L2TP_NET" "L2TP网络段" "${L2TP_NET:-************/24}"
    print_variable "VPN_L2TP_LOCAL" "$VPN_L2TP_LOCAL" "L2TP服务器本地IP" "${L2TP_LOCAL:-************}"
    print_variable "VPN_L2TP_POOL" "$VPN_L2TP_POOL" "L2TP客户端IP池" "${L2TP_POOL:-************0-**************}"
    
    print_variable "VPN_XAUTH_NET" "$VPN_XAUTH_NET" "XAUTH网络段" "${XAUTH_NET:-************/24}"
    print_variable "VPN_XAUTH_POOL" "$VPN_XAUTH_POOL" "XAUTH客户端IP池" "${XAUTH_POOL:-*************-**************}"
    
    print_variable "VPN_SWAN_VER" "$VPN_SWAN_VER" "指定的Libreswan版本" ""
    print_variable "VPN_SKIP_IKEV2" "$VPN_SKIP_IKEV2" "跳过IKEv2设置" ""
    print_variable "VPN_CLIENT_NAME" "$VPN_CLIENT_NAME" "IKEv2客户端名称" ""
    print_variable "VPN_PROTECT_CONFIG" "$VPN_PROTECT_CONFIG" "保护配置文件" ""
    print_variable "VPN_CLIENT_VALIDITY" "$VPN_CLIENT_VALIDITY" "客户端证书有效期" ""
}

# 显示系统检测变量
show_system_variables() {
    print_section "系统检测变量"
    
    print_variable "SYS_DT" "$SYS_DT" "系统时间戳(用于备份文件命名)" ""
    print_variable "NET_IFACE" "$NET_IFACE" "检测到的默认网络接口名" ""
    print_variable "public_ip" "$public_ip" "检测到的服务器公网IP" ""
    print_variable "os_type" "$os_type" "操作系统类型" ""
    print_variable "os_ver" "$os_ver" "操作系统版本号" ""
    print_variable "SWAN_VER" "$SWAN_VER" "使用的Libreswan版本" ""
    print_variable "use_nft" "$use_nft" "是否使用nftables(0/1)" ""
}

# 显示配置处理变量
show_config_variables() {
    print_section "配置处理变量"
    
    print_variable "L2TP_NET" "$L2TP_NET" "处理后的L2TP网络段" ""
    print_variable "L2TP_LOCAL" "$L2TP_LOCAL" "处理后的L2TP本地IP" ""
    print_variable "L2TP_POOL" "$L2TP_POOL" "处理后的L2TP IP池" ""
    print_variable "XAUTH_NET" "$XAUTH_NET" "处理后的XAUTH网络段" ""
    print_variable "XAUTH_POOL" "$XAUTH_POOL" "处理后的XAUTH IP池" ""
    print_variable "DNS_SRV1" "$DNS_SRV1" "处理后的主DNS" ""
    print_variable "DNS_SRV2" "$DNS_SRV2" "处理后的备用DNS" ""
    
    # 计算DNS_SRVS
    if [ -n "$DNS_SRV1" ] && [ -n "$DNS_SRV2" ]; then
        DNS_SRVS="\"$DNS_SRV1 $DNS_SRV2\""
    elif [ -n "$DNS_SRV1" ]; then
        DNS_SRVS="$DNS_SRV1"
    fi
    print_variable "DNS_SRVS" "$DNS_SRVS" "格式化的DNS服务器字符串" ""
    
    # 如果有密码，显示加密后的版本
    if [ -n "$VPN_PASSWORD" ] && command -v openssl >/dev/null 2>&1; then
        VPN_PASSWORD_ENC=$(echo "$VPN_PASSWORD" | openssl passwd -1 -stdin 2>/dev/null)
        print_variable "VPN_PASSWORD_ENC" "$VPN_PASSWORD_ENC" "MD5加密后的VPN密码" ""
    fi
}

# 显示路径和文件变量
show_path_variables() {
    print_section "路径和文件变量"
    
    print_variable "PATH" "$PATH" "系统PATH环境变量" ""
    
    # 防火墙配置文件路径
    if [ "$use_nft" = 1 ]; then
        IPT_FILE="/etc/sysconfig/nftables.conf"
    else
        IPT_FILE="/etc/sysconfig/iptables"
    fi
    print_variable "IPT_FILE" "$IPT_FILE" "防火墙配置文件路径" ""
    
    print_variable "F2B_FILE" "/etc/fail2ban/jail.local" "Fail2Ban配置文件路径" ""
    
    # 检查文件是否存在
    local config_files=(
        "/etc/ipsec.conf:IPsec主配置文件"
        "/etc/ipsec.secrets:IPsec密钥文件"
        "/etc/xl2tpd/xl2tpd.conf:xl2tpd配置文件"
        "/etc/ppp/options.xl2tpd:PPP选项文件"
        "/etc/ppp/chap-secrets:用户认证文件"
        "/etc/ipsec.d/passwd:XAUTH用户文件"
    )
    
    echo -e "${CYAN}配置文件存在状态:${NC}"
    for item in "${config_files[@]}"; do
        local file="${item%%:*}"
        local desc="${item##*:}"
        if [ -f "$file" ]; then
            echo -e "  ${GREEN}✓${NC} $desc: $file"
        else
            echo -e "  ${RED}✗${NC} $desc: $file"
        fi
    done
    echo
}

# 显示网络端口信息
show_network_info() {
    print_section "网络和端口信息"
    
    # VPN端口
    local vpn_ports=(
        "500:UDP:IKE协议端口"
        "4500:UDP:IPsec NAT-T端口"
        "1701:UDP:L2TP端口"
    )
    
    echo -e "${CYAN}VPN端口监听状态:${NC}"
    for item in "${vpn_ports[@]}"; do
        local port="${item%%:*}"
        local proto="${item#*:}"
        proto="${proto%%:*}"
        local desc="${item##*:}"
        
        if command -v ss >/dev/null 2>&1; then
            if ss -ulnp 2>/dev/null | grep -q ":$port "; then
                echo -e "  ${GREEN}✓${NC} $desc ($proto $port) 正在监听"
            else
                echo -e "  ${RED}✗${NC} $desc ($proto $port) 未监听"
            fi
        else
            echo -e "  ${YELLOW}?${NC} $desc ($proto $port) 无法检测"
        fi
    done
    echo
    
    # IP转发状态
    local ip_forward=$(cat /proc/sys/net/ipv4/ip_forward 2>/dev/null)
    print_variable "net.ipv4.ip_forward" "$ip_forward" "IP转发状态(1=启用,0=禁用)" ""
}

# 显示服务状态
show_service_status() {
    print_section "VPN服务状态"
    
    local services=(
        "ipsec:IPsec服务"
        "xl2tpd:L2TP服务"
        "fail2ban:Fail2Ban服务"
        "firewalld:Firewalld服务"
        "nftables:NFTables服务"
        "iptables:IPTables服务"
    )
    
    for item in "${services[@]}"; do
        local service="${item%%:*}"
        local desc="${item##*:}"
        
        if systemctl is-active --quiet "$service" 2>/dev/null; then
            local status="运行中"
            local color="$GREEN"
        else
            local status="未运行"
            local color="$RED"
        fi
        
        if systemctl is-enabled --quiet "$service" 2>/dev/null; then
            local enabled="已启用"
        else
            local enabled="未启用"
        fi
        
        echo -e "  ${color}$desc${NC}: $status / $enabled"
    done
    echo
}

# 主函数
main() {
    print_header "VPN变量完整显示报告"
    
    echo "生成时间: $(date)"
    echo "操作系统: $(cat /etc/redhat-release 2>/dev/null || echo '未知')"
    echo "内核版本: $(uname -r)"
    echo "当前用户: $(whoami)"
    echo ""
    
    check_root
    
    # 模拟脚本环境
    simulate_script_environment
    
    # 从配置文件读取变量
    read_config_variables
    
    # 显示各类变量
    show_user_configurable_variables
    show_system_variables
    show_config_variables
    show_path_variables
    show_network_info
    show_service_status
    
    print_header "报告完成"
    echo "如需保存此报告，请运行: bash $0 > vpn_variables_report.txt"
}

# 运行主函数
main "$@"

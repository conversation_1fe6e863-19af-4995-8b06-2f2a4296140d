#!/bin/bash
#
# L2TP VPN用户管理脚本
# 用于管理xl2tpd VPN服务器的用户账户
#

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置文件路径
CHAP_SECRETS="/etc/ppp/chap-secrets"
BACKUP_DIR="/root/vpn_backups"

# 输出函数
print_success() { echo -e "${GREEN}✓ $1${NC}"; }
print_error() { echo -e "${RED}✗ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠ $1${NC}"; }
print_info() { echo -e "${BLUE}ℹ $1${NC}"; }

# 检查root权限
check_root() {
    if [ "$(id -u)" != 0 ]; then
        print_error "此脚本需要root权限运行。请使用 'sudo bash $0'"
        exit 1
    fi
}

# 备份配置文件
backup_config() {
    mkdir -p "$BACKUP_DIR"
    local timestamp=$(date +%Y%m%d_%H%M%S)
    cp "$CHAP_SECRETS" "$BACKUP_DIR/chap-secrets_$timestamp" 2>/dev/null
    print_info "配置文件已备份到: $BACKUP_DIR/chap-secrets_$timestamp"
}

# 验证用户名格式
validate_username() {
    local username="$1"
    if [ -z "$username" ]; then
        print_error "用户名不能为空"
        return 1
    fi
    
    if [ ${#username} -gt 32 ]; then
        print_error "用户名长度不能超过32个字符"
        return 1
    fi
    
    if ! echo "$username" | grep -qE '^[a-zA-Z0-9_-]+$'; then
        print_error "用户名只能包含字母、数字、下划线和连字符"
        return 1
    fi
    
    return 0
}

# 验证密码格式
validate_password() {
    local password="$1"
    if [ -z "$password" ]; then
        print_error "密码不能为空"
        return 1
    fi
    
    if [ ${#password} -lt 6 ]; then
        print_error "密码长度不能少于6个字符"
        return 1
    fi
    
    if [ ${#password} -gt 64 ]; then
        print_error "密码长度不能超过64个字符"
        return 1
    fi
    
    # 检查是否包含特殊字符
    if echo "$password" | grep -q '["\'\]'; then
        print_error "密码不能包含双引号、单引号或反斜杠"
        return 1
    fi
    
    return 0
}

# 检查用户是否存在
user_exists() {
    local username="$1"
    grep -q "^\"$username\"" "$CHAP_SECRETS" 2>/dev/null
}

# 添加用户
add_user() {
    local username="$1"
    local password="$2"
    
    # 如果没有提供参数，交互式输入
    if [ -z "$username" ]; then
        echo -n "请输入用户名: "
        read -r username
    fi
    
    if [ -z "$password" ]; then
        echo -n "请输入密码: "
        read -s password
        echo
        echo -n "请确认密码: "
        read -s password_confirm
        echo
        
        if [ "$password" != "$password_confirm" ]; then
            print_error "两次输入的密码不一致"
            return 1
        fi
    fi
    
    # 验证输入
    validate_username "$username" || return 1
    validate_password "$password" || return 1
    
    # 检查用户是否已存在
    if user_exists "$username"; then
        print_error "用户 '$username' 已存在"
        return 1
    fi
    
    # 备份配置文件
    backup_config
    
    # 添加用户
    echo "\"$username\" l2tpd \"$password\" *" >> "$CHAP_SECRETS"
    
    if [ $? -eq 0 ]; then
        print_success "用户 '$username' 添加成功"
        print_info "用户名: $username"
        print_info "密码: $password"
        print_info "协议: L2TP"
        
        # 重启xl2tpd服务
        systemctl restart xl2tpd
        print_info "xl2tpd服务已重启"
    else
        print_error "添加用户失败"
        return 1
    fi
}

# 删除用户
delete_user() {
    local username="$1"
    
    # 如果没有提供参数，交互式输入
    if [ -z "$username" ]; then
        echo -n "请输入要删除的用户名: "
        read -r username
    fi
    
    # 验证用户名
    validate_username "$username" || return 1
    
    # 检查用户是否存在
    if ! user_exists "$username"; then
        print_error "用户 '$username' 不存在"
        return 1
    fi
    
    # 确认删除
    echo -n "确定要删除用户 '$username' 吗？(y/N): "
    read -r confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        print_info "操作已取消"
        return 0
    fi
    
    # 备份配置文件
    backup_config
    
    # 删除用户
    sed -i "/^\"$username\"/d" "$CHAP_SECRETS"
    
    if [ $? -eq 0 ]; then
        print_success "用户 '$username' 删除成功"
        
        # 重启xl2tpd服务
        systemctl restart xl2tpd
        print_info "xl2tpd服务已重启"
    else
        print_error "删除用户失败"
        return 1
    fi
}

# 修改用户密码
change_password() {
    local username="$1"
    local new_password="$2"
    
    # 如果没有提供参数，交互式输入
    if [ -z "$username" ]; then
        echo -n "请输入用户名: "
        read -r username
    fi
    
    # 验证用户名
    validate_username "$username" || return 1
    
    # 检查用户是否存在
    if ! user_exists "$username"; then
        print_error "用户 '$username' 不存在"
        return 1
    fi
    
    if [ -z "$new_password" ]; then
        echo -n "请输入新密码: "
        read -s new_password
        echo
        echo -n "请确认新密码: "
        read -s password_confirm
        echo
        
        if [ "$new_password" != "$password_confirm" ]; then
            print_error "两次输入的密码不一致"
            return 1
        fi
    fi
    
    # 验证密码
    validate_password "$new_password" || return 1
    
    # 备份配置文件
    backup_config
    
    # 修改密码
    sed -i "s/^\"$username\" l2tpd \".*\" \*/\"$username\" l2tpd \"$new_password\" */" "$CHAP_SECRETS"
    
    if [ $? -eq 0 ]; then
        print_success "用户 '$username' 密码修改成功"
        print_info "新密码: $new_password"
        
        # 重启xl2tpd服务
        systemctl restart xl2tpd
        print_info "xl2tpd服务已重启"
    else
        print_error "修改密码失败"
        return 1
    fi
}

# 列出所有用户
list_users() {
    print_info "L2TP VPN用户列表:"
    echo ""
    
    if [ ! -f "$CHAP_SECRETS" ]; then
        print_error "配置文件不存在: $CHAP_SECRETS"
        return 1
    fi
    
    local user_count=0
    echo "用户名                密码                  协议"
    echo "----------------------------------------------------"
    
    while IFS= read -r line; do
        # 跳过注释和空行
        if [[ "$line" =~ ^[[:space:]]*# ]] || [[ -z "$line" ]]; then
            continue
        fi
        
        # 解析用户信息
        local username=$(echo "$line" | awk '{print $1}' | tr -d '"')
        local password=$(echo "$line" | awk '{print $3}' | tr -d '"')
        local protocol=$(echo "$line" | awk '{print $2}')
        
        if [ -n "$username" ]; then
            printf "%-20s %-20s %-10s\n" "$username" "$password" "$protocol"
            ((user_count++))
        fi
    done < "$CHAP_SECRETS"
    
    echo "----------------------------------------------------"
    print_info "总用户数: $user_count"
}

# 生成随机密码
generate_password() {
    local length=${1:-12}
    LC_CTYPE=C tr -dc 'A-HJ-NPR-Za-km-z2-9' </dev/urandom 2>/dev/null | head -c "$length"
}

# 批量添加用户
batch_add_users() {
    local count=${1:-1}
    local prefix=${2:-"user"}
    
    print_info "批量添加 $count 个用户，用户名前缀: $prefix"
    echo ""
    
    for i in $(seq 1 "$count"); do
        local username="${prefix}${i}"
        local password=$(generate_password 12)
        
        if user_exists "$username"; then
            print_warning "用户 '$username' 已存在，跳过"
            continue
        fi
        
        # 备份配置文件（仅第一次）
        if [ "$i" -eq 1 ]; then
            backup_config
        fi
        
        # 添加用户
        echo "\"$username\" l2tpd \"$password\" *" >> "$CHAP_SECRETS"
        print_success "添加用户: $username / $password"
    done
    
    # 重启服务
    systemctl restart xl2tpd
    print_info "xl2tpd服务已重启"
}

# 显示帮助信息
show_help() {
    echo "L2TP VPN用户管理脚本"
    echo ""
    echo "用法: $0 [选项] [参数]"
    echo ""
    echo "选项:"
    echo "  add [用户名] [密码]     添加新用户"
    echo "  del [用户名]           删除用户"
    echo "  passwd [用户名] [密码] 修改用户密码"
    echo "  list                   列出所有用户"
    echo "  batch [数量] [前缀]    批量添加用户"
    echo "  help                   显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 add john mypassword123"
    echo "  $0 del john"
    echo "  $0 passwd john newpassword456"
    echo "  $0 list"
    echo "  $0 batch 5 testuser"
    echo ""
    echo "注意:"
    echo "  - 用户名只能包含字母、数字、下划线和连字符"
    echo "  - 密码长度6-64个字符，不能包含引号和反斜杠"
    echo "  - 所有操作都会自动备份配置文件"
}

# 主函数
main() {
    check_root
    
    local action="$1"
    
    case "$action" in
        "add")
            add_user "$2" "$3"
            ;;
        "del"|"delete")
            delete_user "$2"
            ;;
        "passwd"|"password")
            change_password "$2" "$3"
            ;;
        "list"|"ls")
            list_users
            ;;
        "batch")
            batch_add_users "$2" "$3"
            ;;
        "help"|"-h"|"--help"|"")
            show_help
            ;;
        *)
            print_error "未知选项: $action"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"

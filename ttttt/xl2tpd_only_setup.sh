#!/bin/bash
#
# 纯L2TP/IPsec VPN服务器安装脚本 - 仅启用xl2tpd
# 基于原始vpnsetup_centos.sh脚本简化而来
# 
# 警告：请勿在个人电脑或Mac上运行此脚本！
#
# 支持系统：CentOS/RHEL/Rocky Linux/AlmaLinux/Oracle Linux 7-10
#

# =====================================================
# 用户配置区域
# =====================================================

# 定义您的VPN凭据（留空则自动生成）
YOUR_IPSEC_PSK=''    # IPsec预共享密钥
YOUR_USERNAME=''     # VPN用户名
YOUR_PASSWORD=''     # VPN密码

# L2TP网络配置（可选自定义）
VPN_L2TP_NET=${VPN_L2TP_NET:-'************/24'}
VPN_L2TP_LOCAL=${VPN_L2TP_LOCAL:-'************'}
VPN_L2TP_POOL=${VPN_L2TP_POOL:-'************0-**************'}

# DNS服务器配置
VPN_DNS_SRV1=${VPN_DNS_SRV1:-'*******'}
VPN_DNS_SRV2=${VPN_DNS_SRV2:-'*******'}

# =====================================================
# 脚本初始化
# =====================================================

export PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
SYS_DT=$(date +%F-%T | tr ':' '_')

# 工具函数
exiterr()  { echo "错误: $1" >&2; exit 1; }
exiterr2() { exiterr "dnf安装失败"; }
conf_bk() { /bin/cp -f "$1" "$1.old-$SYS_DT" 2>/dev/null; }
bigecho() { echo "## $1"; }

# IP地址验证函数
check_ip() {
  IP_REGEX='^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$'
  printf '%s' "$1" | tr -d '\n' | grep -Eq "$IP_REGEX"
}

# =====================================================
# 系统检查函数
# =====================================================

# 检查root权限
check_root() {
  if [ "$(id -u)" != 0 ]; then
    exiterr "脚本必须以root权限运行。请使用 'sudo bash $0'"
  fi
}

# 检查操作系统
check_os() {
  rh_file="/etc/redhat-release"
  if [ -f "$rh_file" ]; then
    os_type=centos
    if grep -q "Red Hat" "$rh_file"; then
      os_type=rhel
    fi
    [ -f /etc/oracle-release ] && os_type=ol
    grep -qi rocky "$rh_file" && os_type=rocky
    grep -qi alma "$rh_file" && os_type=alma
    
    if grep -q "release 7" "$rh_file"; then
      os_ver=7
    elif grep -q "release 8" "$rh_file"; then
      os_ver=8
    elif grep -q "release 9" "$rh_file"; then
      os_ver=9
    elif grep -q "release 10" "$rh_file"; then
      os_ver=10
    else
      exiterr "此脚本仅支持CentOS/RHEL 7-10"
    fi
    
    if [ "$os_type" = "centos" ] && { [ "$os_ver" = 7 ] || [ "$os_ver" = 8 ]; }; then
      exiterr "CentOS Linux $os_ver 已停止支持"
    fi
  else
    exiterr "此脚本仅支持CentOS/RHEL、Rocky Linux、AlmaLinux或Oracle Linux"
  fi
}

# 检查网络接口
check_iface() {
  if ! command -v route >/dev/null 2>&1 && ! command -v ip >/dev/null 2>&1; then
    dnf -y -q install iproute >/dev/null 2>&1
  fi
  
  def_iface=$(route 2>/dev/null | grep -m 1 '^default' | grep -o '[^ ]*$')
  [ -z "$def_iface" ] && def_iface=$(ip -4 route list 0/0 2>/dev/null | grep -m 1 -Po '(?<=dev )(\S+)')
  
  def_state=$(cat "/sys/class/net/$def_iface/operstate" 2>/dev/null)
  if [ -n "$def_state" ] && [ "$def_state" != "down" ]; then
    case $def_iface in
      wl*)
        exiterr "检测到无线接口 '$def_iface'。请勿在个人电脑或Mac上运行此脚本！"
        ;;
    esac
    NET_IFACE="$def_iface"
  else
    NET_IFACE=eth0
  fi
}

# 检查和设置VPN凭据
check_creds() {
  [ -n "$YOUR_IPSEC_PSK" ] && VPN_IPSEC_PSK="$YOUR_IPSEC_PSK"
  [ -n "$YOUR_USERNAME" ] && VPN_USER="$YOUR_USERNAME"
  [ -n "$YOUR_PASSWORD" ] && VPN_PASSWORD="$YOUR_PASSWORD"
  
  if [ -z "$VPN_IPSEC_PSK" ] && [ -z "$VPN_USER" ] && [ -z "$VPN_PASSWORD" ]; then
    bigecho "用户未设置VPN凭据，正在生成随机PSK和密码..."
    VPN_IPSEC_PSK=$(LC_CTYPE=C tr -dc 'A-HJ-NPR-Za-km-z2-9' </dev/urandom 2>/dev/null | head -c 20)
    VPN_USER=vpnuser
    VPN_PASSWORD=$(LC_CTYPE=C tr -dc 'A-HJ-NPR-Za-km-z2-9' </dev/urandom 2>/dev/null | head -c 16)
  fi
  
  if [ -z "$VPN_IPSEC_PSK" ] || [ -z "$VPN_USER" ] || [ -z "$VPN_PASSWORD" ]; then
    exiterr "必须指定所有VPN凭据。请编辑脚本并重新输入。"
  fi
  
  case "$VPN_IPSEC_PSK $VPN_USER $VPN_PASSWORD" in
    *[\\\"\']*)
      exiterr "VPN凭据不能包含这些特殊字符: \\ \" '"
      ;;
  esac
}

# =====================================================
# 安装和配置函数
# =====================================================

# 检测公网IP
detect_ip() {
  public_ip=${VPN_PUBLIC_IP:-''}
  
  if ! check_ip "$public_ip"; then
    def_ip=$(ip -4 route get 1 | sed 's/ uid .*//' | awk '{print $NF;exit}' 2>/dev/null)
    if check_ip "$def_ip" && ! printf '%s' "$def_ip" | grep -Eq '^(10|127|172\.(1[6-9]|2[0-9]|3[0-1])|192\.168|169\.254)\.'; then
      public_ip="$def_ip"
    fi
  fi
  
  if ! check_ip "$public_ip"; then
    bigecho "正在尝试自动发现服务器IP..."
    public_ip=$(dig @resolver1.opendns.com -t A -4 myip.opendns.com +short 2>/dev/null)
    check_ip "$public_ip" || public_ip=$(wget -t 2 -T 10 -qO- http://ipv4.icanhazip.com 2>/dev/null)
    check_ip "$public_ip" || public_ip=$(wget -t 2 -T 10 -qO- http://ip1.dynupdate.no-ip.com 2>/dev/null)
  fi
  
  check_ip "$public_ip" || exiterr "无法检测到服务器的公网IP。请设置VPN_PUBLIC_IP变量并重新运行脚本。"
}

# 安装基础软件包
install_packages() {
  bigecho "安装所需软件包..."
  
  # 添加EPEL源
  epel_url="https://dl.fedoraproject.org/pub/epel/epel-release-latest-$(rpm -E '%{rhel}').noarch.rpm"
  dnf -y -q install epel-release >/dev/null 2>&1 || dnf -y -q install "$epel_url" >/dev/null 2>&1
  
  # 安装基础工具
  dnf -y -q install wget bind-utils openssl tar iptables iproute >/dev/null || exiterr2
  
  # 安装Libreswan编译依赖
  dnf -y -q install nss-devel nspr-devel pkgconfig pam-devel \
    libcap-ng-devel libselinux-devel curl-devel nss-tools \
    flex bison gcc make util-linux ppp >/dev/null || exiterr2
  
  # 安装xl2tpd
  dnf --enablerepo=epel -y -q install xl2tpd >/dev/null 2>&1 || exiterr2
  
  # 安装系统服务包
  if [ "$os_ver" = 7 ]; then
    dnf -y -q install systemd-devel libevent-devel fipscheck-devel iptables-services >/dev/null || exiterr2
    use_nft=0
  else
    dnf -y -q install systemd-devel libevent-devel >/dev/null || exiterr2
    if [ "$os_ver" = 9 ] || [ "$os_ver" = 10 ] || systemctl is-active --quiet firewalld; then
      dnf -y -q install nftables >/dev/null || exiterr2
      use_nft=1
    else
      dnf -y -q install iptables-services >/dev/null || exiterr2
      use_nft=0
    fi
  fi
  
  # 安装fail2ban
  dnf --enablerepo=epel -y -q install fail2ban >/dev/null 2>&1
}

# 安装Libreswan
install_libreswan() {
  bigecho "下载并编译安装Libreswan..."
  
  mkdir -p /opt/src
  cd /opt/src || exit 1
  
  # 获取Libreswan版本
  SWAN_VER=5.2
  base_url="https://github.com/hwdsl2/vpn-extras/releases/download/v1.0.0"
  swan_ver_url="$base_url/v1-$os_type-$os_ver-swanver"
  swan_ver_latest=$(wget -t 2 -T 10 -qO- "$swan_ver_url" 2>/dev/null | head -n 1)
  if printf '%s' "$swan_ver_latest" | grep -Eq '^([3-9]|[1-9][0-9]{1,2})(\.([0-9]|[1-9][0-9]{1,2})){1,2}$'; then
    SWAN_VER="$swan_ver_latest"
  fi
  
  # 下载源码
  swan_file="libreswan-$SWAN_VER.tar.gz"
  swan_url1="https://github.com/libreswan/libreswan/archive/v$SWAN_VER.tar.gz"
  swan_url2="https://download.libreswan.org/$swan_file"
  
  wget -t 3 -T 30 -q -O "$swan_file" "$swan_url1" || wget -t 3 -T 30 -q -O "$swan_file" "$swan_url2" || exit 1
  
  /bin/rm -rf "/opt/src/libreswan-$SWAN_VER"
  tar xzf "$swan_file" && /bin/rm -f "$swan_file"
  
  # 编译安装
  cd "libreswan-$SWAN_VER" || exit 1
  
cat > Makefile.inc.local <<'EOF'
WERROR_CFLAGS=-w -s
USE_DNSSEC=false
USE_DH2=true
USE_NSS_KDF=false
USE_LINUX_AUDIT=false
USE_SECCOMP=false
FINALNSSDIR=/etc/ipsec.d
NSSDIR=/etc/ipsec.d
EOF
  
  if ! grep -qs IFLA_XFRM_LINK /usr/include/linux/if_link.h; then
    echo "USE_XFRM_INTERFACE_IFLA_HEADER=true" >> Makefile.inc.local
  fi
  
  NPROCS=$(grep -c ^processor /proc/cpuinfo)
  [ -z "$NPROCS" ] && NPROCS=1
  
  make "-j$((NPROCS+1))" -s base >/dev/null 2>&1 && make -s install-base >/dev/null 2>&1
  
  cd /opt/src || exit 1
  /bin/rm -rf "/opt/src/libreswan-$SWAN_VER"
  
  if ! /usr/local/sbin/ipsec --version 2>/dev/null | grep -qF "$SWAN_VER"; then
    exiterr "Libreswan $SWAN_VER 编译失败"
  fi
}

# =====================================================
# VPN配置文件创建
# =====================================================

# 创建L2TP/IPsec配置文件
create_vpn_config() {
  bigecho "创建VPN配置文件..."

  # 设置网络参数
  L2TP_NET="$VPN_L2TP_NET"
  L2TP_LOCAL="$VPN_L2TP_LOCAL"
  L2TP_POOL="$VPN_L2TP_POOL"
  DNS_SRV1="$VPN_DNS_SRV1"
  DNS_SRV2="$VPN_DNS_SRV2"

  # 创建IPsec配置文件（仅L2TP模式）
  conf_bk "/etc/ipsec.conf"
cat > /etc/ipsec.conf <<EOF
version 2.0

config setup
  ikev1-policy=accept
  virtual-private=%v4:10.0.0.0/8,%v4:***********/16,%v4:**********/12,%v4:!$L2TP_NET
  uniqueids=no

conn shared
  left=%defaultroute
  leftid=$public_ip
  right=%any
  encapsulation=yes
  authby=secret
  pfs=no
  rekey=no
  keyingtries=5
  dpddelay=30
  dpdtimeout=300
  dpdaction=clear
  ikev2=never
  ike=aes256-sha2;modp2048,aes128-sha2;modp2048,aes256-sha1;modp2048,aes128-sha1;modp2048
  phase2alg=aes_gcm-null,aes128-sha1,aes256-sha1,aes256-sha2_512,aes128-sha2,aes256-sha2
  ikelifetime=24h
  salifetime=24h
  sha2-truncbug=no

conn l2tp-psk
  auto=add
  leftprotoport=17/1701
  rightprotoport=17/%any
  type=transport
  also=shared

include /etc/ipsec.d/*.conf
EOF

  # 创建IPsec密钥文件
  conf_bk "/etc/ipsec.secrets"
cat > /etc/ipsec.secrets <<EOF
%any  %any  : PSK "$VPN_IPSEC_PSK"
EOF

  # 创建xl2tpd配置文件
  conf_bk "/etc/xl2tpd/xl2tpd.conf"
cat > /etc/xl2tpd/xl2tpd.conf <<EOF
[global]
port = 1701

[lns default]
ip range = $L2TP_POOL
local ip = $L2TP_LOCAL
require chap = yes
refuse pap = yes
require authentication = yes
name = l2tpd
pppoptfile = /etc/ppp/options.xl2tpd
length bit = yes
EOF

  # 创建PPP选项文件
  conf_bk "/etc/ppp/options.xl2tpd"
cat > /etc/ppp/options.xl2tpd <<EOF
+mschap-v2
ipcp-accept-local
ipcp-accept-remote
noccp
auth
mtu 1280
mru 1280
proxyarp
lcp-echo-failure 4
lcp-echo-interval 30
connect-delay 5000
ms-dns $DNS_SRV1
ms-dns $DNS_SRV2
EOF

  # 创建用户认证文件
  conf_bk "/etc/ppp/chap-secrets"
cat > /etc/ppp/chap-secrets <<EOF
"$VPN_USER" l2tpd "$VPN_PASSWORD" *
EOF
}

# 配置系统参数
configure_system() {
  bigecho "配置系统参数..."

  # 更新sysctl设置
  if ! grep -qs "xl2tpd VPN script" /etc/sysctl.conf; then
    conf_bk "/etc/sysctl.conf"
cat >> /etc/sysctl.conf <<EOF

# Added by xl2tpd VPN script
kernel.msgmnb = 65536
kernel.msgmax = 65536

net.ipv4.ip_forward = 1
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.all.rp_filter = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.conf.default.rp_filter = 0
net.ipv4.conf.$NET_IFACE.send_redirects = 0
net.ipv4.conf.$NET_IFACE.rp_filter = 0

net.core.wmem_max = 16777216
net.core.rmem_max = 16777216
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 87380 16777216
EOF

    # 启用BBR（如果支持）
    if modprobe -q tcp_bbr && printf '%s\n%s' "4.20" "$(uname -r)" | sort -C -V; then
cat >> /etc/sysctl.conf <<'EOF'
net.core.default_qdisc = fq
net.ipv4.tcp_congestion_control = bbr
EOF
    fi
  fi
}

# 配置防火墙
configure_firewall() {
  bigecho "配置防火墙规则..."

  # 确定防火墙配置文件
  if [ "$use_nft" = 1 ]; then
    IPT_FILE=/etc/sysconfig/nftables.conf
  else
    IPT_FILE=/etc/sysconfig/iptables
  fi

  # 检查是否已配置
  if ! grep -qs "xl2tpd VPN script" "$IPT_FILE"; then
    # 停止fail2ban
    service fail2ban stop >/dev/null 2>&1

    if [ "$use_nft" = 1 ]; then
      # 备份nftables规则
      nft list ruleset > "$IPT_FILE.old-$SYS_DT" 2>/dev/null
      chmod 600 "$IPT_FILE.old-$SYS_DT" 2>/dev/null

      # 禁用firewalld的nftables所有权
      fd_conf=/etc/firewalld/firewalld.conf
      if grep -qs '^NftablesTableOwner=yes' "$fd_conf"; then
        sed -i '/NftablesTableOwner/s/yes/no/' "$fd_conf"
        firewall-cmd --reload >/dev/null 2>&1
      fi
    else
      # 备份iptables规则
      iptables-save > "$IPT_FILE.old-$SYS_DT" 2>/dev/null
    fi

    # 配置防火墙规则
    ipi='iptables -I INPUT'
    ipf='iptables -I FORWARD'
    ipp='iptables -t nat -I POSTROUTING'
    res='RELATED,ESTABLISHED'

    # INPUT规则
    $ipi 1 -p udp --dport 1701 -m policy --dir in --pol none -j DROP
    $ipi 2 -m conntrack --ctstate INVALID -j DROP
    $ipi 3 -m conntrack --ctstate "$res" -j ACCEPT
    $ipi 4 -p udp -m multiport --dports 500,4500 -j ACCEPT
    $ipi 5 -p udp --dport 1701 -m policy --dir in --pol ipsec -j ACCEPT
    $ipi 6 -p udp --dport 1701 -j DROP

    # FORWARD规则
    $ipf 1 -m conntrack --ctstate INVALID -j DROP
    $ipf 2 -i "$NET_IFACE" -o ppp+ -m conntrack --ctstate "$res" -j ACCEPT
    $ipf 3 -i ppp+ -o "$NET_IFACE" -j ACCEPT
    $ipf 4 -i ppp+ -o ppp+ -j ACCEPT

    # NAT规则
    $ipp -s "$L2TP_NET" -o "$NET_IFACE" -j MASQUERADE

    # 保存规则
    echo "# Modified by xl2tpd VPN script" > "$IPT_FILE"
    if [ "$use_nft" = 1 ]; then
      echo "flush ruleset" >> "$IPT_FILE"
      nft list ruleset >> "$IPT_FILE"
    else
      iptables-save >> "$IPT_FILE"
    fi
  fi
}

# 配置Fail2Ban
configure_fail2ban() {
  F2B_FILE=/etc/fail2ban/jail.local
  if [ ! -f "$F2B_FILE" ]; then
    bigecho "配置Fail2Ban..."
cat > "$F2B_FILE" <<'EOF'
[ssh-iptables]
enabled = true
filter = sshd
logpath = /var/log/secure
EOF

    if [ "$use_nft" = 1 ]; then
cat >> "$F2B_FILE" <<'EOF'
port = ssh
banaction = nftables-multiport[blocktype=drop]
EOF
    else
cat >> "$F2B_FILE" <<'EOF'
action = iptables[name=SSH, port=ssh, protocol=tcp]
EOF
    fi
  fi
}

# =====================================================
# 服务启动和配置
# =====================================================

# 配置开机自启动
enable_services() {
  bigecho "配置服务开机自启动..."

  # 屏蔽firewalld
  systemctl --now mask firewalld 2>/dev/null

  # 启用服务
  if [ "$use_nft" = 1 ]; then
    systemctl enable nftables 2>/dev/null
  else
    systemctl enable iptables 2>/dev/null
  fi
  systemctl enable fail2ban 2>/dev/null

  # 配置开机启动脚本
  if ! grep -qs "xl2tpd VPN script" /etc/rc.local; then
    if [ -f /etc/rc.local ]; then
      conf_bk "/etc/rc.local"
    else
      echo '#!/bin/sh' > /etc/rc.local
    fi

cat >> /etc/rc.local <<'EOF'

# Added by xl2tpd VPN script
(sleep 15
service ipsec restart
service xl2tpd restart
echo 1 > /proc/sys/net/ipv4/ip_forward)&
EOF
  fi
}

# 启动VPN服务
start_services() {
  bigecho "启动VPN服务..."

  # 应用系统配置
  sysctl -e -q -p

  # 设置文件权限
  chmod +x /etc/rc.local
  chmod 600 /etc/ipsec.secrets* /etc/ppp/chap-secrets*

  # 恢复SELinux上下文
  restorecon /etc/ipsec.d/*db 2>/dev/null
  restorecon /usr/local/sbin -Rv 2>/dev/null
  restorecon /usr/local/libexec/ipsec -Rv 2>/dev/null

  # 应用防火墙规则
  if [ "$use_nft" = 1 ]; then
    nft -f "$IPT_FILE" 2>/dev/null
  else
    iptables-restore < "$IPT_FILE" 2>/dev/null
  fi

  # 修复xl2tpd服务（如果需要）
  if ! modprobe -q l2tp_ppp; then
    sed -i '/^ExecStartPre=\//s/=/=-/' /usr/lib/systemd/system/xl2tpd.service 2>/dev/null
    systemctl daemon-reload
  fi

  # 创建运行目录
  mkdir -p /run/pluto

  # 启动服务
  service fail2ban restart 2>/dev/null
  service ipsec restart 2>/dev/null
  service xl2tpd restart 2>/dev/null

  # 等待服务启动
  sleep 3
}

# 显示VPN信息
show_vpn_info() {
cat <<EOF

================================================

L2TP/IPsec VPN服务器安装完成！

请使用以下信息连接到您的VPN：

服务器IP: $public_ip
IPsec PSK: $VPN_IPSEC_PSK
用户名: $VPN_USER
密码: $VPN_PASSWORD

L2TP网络段: $VPN_L2TP_NET
客户端IP池: $VPN_L2TP_POOL
DNS服务器: $VPN_DNS_SRV1, $VPN_DNS_SRV2

请记录这些信息，连接时需要使用！

客户端配置指南: https://vpnsetup.net/clients

================================================

注意：此VPN仅支持L2TP/IPsec协议
不支持IKEv2和Cisco IPsec (XAUTH)

================================================

EOF
}

# 检查服务状态
check_services() {
  bigecho "检查服务状态..."

  echo "IPsec服务状态:"
  if systemctl is-active --quiet ipsec; then
    echo "  ✓ ipsec服务正在运行"
  else
    echo "  ✗ ipsec服务未运行"
  fi

  echo "xl2tpd服务状态:"
  if systemctl is-active --quiet xl2tpd; then
    echo "  ✓ xl2tpd服务正在运行"
  else
    echo "  ✗ xl2tpd服务未运行"
  fi

  echo "Fail2Ban服务状态:"
  if systemctl is-active --quiet fail2ban; then
    echo "  ✓ fail2ban服务正在运行"
  else
    echo "  ✗ fail2ban服务未运行"
  fi

  echo ""
  echo "监听端口检查:"
  if ss -ulnp 2>/dev/null | grep -q ":500 "; then
    echo "  ✓ UDP 500 (IKE) 正在监听"
  else
    echo "  ✗ UDP 500 (IKE) 未监听"
  fi

  if ss -ulnp 2>/dev/null | grep -q ":4500 "; then
    echo "  ✓ UDP 4500 (NAT-T) 正在监听"
  else
    echo "  ✗ UDP 4500 (NAT-T) 未监听"
  fi

  if ss -ulnp 2>/dev/null | grep -q ":1701 "; then
    echo "  ✓ UDP 1701 (L2TP) 正在监听"
  else
    echo "  ✗ UDP 1701 (L2TP) 未监听"
  fi

  echo ""
}

# =====================================================
# 主安装流程
# =====================================================

# 主安装函数
xl2tpd_setup() {
  echo "开始安装L2TP/IPsec VPN服务器..."
  echo "支持的系统: CentOS/RHEL/Rocky Linux/AlmaLinux/Oracle Linux 7-10"
  echo ""

  check_root              # 检查root权限
  check_os                # 检查操作系统
  check_iface             # 检查网络接口
  check_creds             # 检查VPN凭据
  detect_ip               # 检测公网IP
  install_packages        # 安装软件包
  install_libreswan       # 安装Libreswan
  create_vpn_config       # 创建VPN配置
  configure_system        # 配置系统参数
  configure_firewall      # 配置防火墙
  configure_fail2ban      # 配置Fail2Ban
  enable_services         # 配置开机自启
  start_services          # 启动服务
  show_vpn_info           # 显示连接信息
  check_services          # 检查服务状态

  echo "L2TP/IPsec VPN服务器安装完成！"
}

# =====================================================
# 脚本执行入口
# =====================================================

# 执行安装
xl2tpd_setup "$@"

exit 0

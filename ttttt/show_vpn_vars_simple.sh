#!/bin/bash
#
# VPN变量快速显示脚本
# 简洁版本，只显示关键变量
#

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}=== VPN关键变量显示 ===${NC}"
echo "时间: $(date)"
echo ""

# 1. 系统信息
echo -e "${CYAN}[系统信息]${NC}"
echo "操作系统: $(cat /etc/redhat-release 2>/dev/null || echo '未知')"
echo "内核版本: $(uname -r)"

# 检测网络接口
if command -v ip >/dev/null 2>&1; then
    NET_IFACE=$(ip -4 route list 0/0 2>/dev/null | grep -m 1 -Po '(?<=dev )(\S+)')
fi
[ -z "$NET_IFACE" ] && NET_IFACE="eth0"
echo "网络接口: $NET_IFACE"

# 检测公网IP
if command -v wget >/dev/null 2>&1; then
    PUBLIC_IP=$(wget -t 2 -T 5 -qO- http://ipv4.icanhazip.com 2>/dev/null)
elif command -v curl >/dev/null 2>&1; then
    PUBLIC_IP=$(curl -s --max-time 5 http://ipv4.icanhazip.com 2>/dev/null)
fi
echo "公网IP: ${PUBLIC_IP:-无法检测}"
echo ""

# 2. VPN凭据信息
echo -e "${CYAN}[VPN凭据]${NC}"
if [ -f "/etc/ipsec.secrets" ] && [ -r "/etc/ipsec.secrets" ]; then
    PSK=$(grep "PSK" /etc/ipsec.secrets 2>/dev/null | awk -F'"' '{print $2}' | head -1)
    echo -e "IPsec PSK: ${GREEN}${PSK:-未找到}${NC}"
else
    echo -e "IPsec PSK: ${YELLOW}无法读取 /etc/ipsec.secrets${NC}"
fi

if [ -f "/etc/ppp/chap-secrets" ] && [ -r "/etc/ppp/chap-secrets" ]; then
    USER_LINE=$(grep -v "^#" /etc/ppp/chap-secrets 2>/dev/null | grep -v "^$" | head -1)
    if [ -n "$USER_LINE" ]; then
        USERNAME=$(echo "$USER_LINE" | awk '{print $1}' | tr -d '"')
        PASSWORD=$(echo "$USER_LINE" | awk '{print $3}' | tr -d '"')
        echo -e "VPN用户名: ${GREEN}$USERNAME${NC}"
        echo -e "VPN密码: ${GREEN}$PASSWORD${NC}"
    else
        echo "VPN用户: 未配置"
    fi
else
    echo -e "VPN用户: ${YELLOW}无法读取 /etc/ppp/chap-secrets${NC}"
fi
echo ""

# 3. 网络配置
echo -e "${CYAN}[网络配置]${NC}"
if [ -f "/etc/xl2tpd/xl2tpd.conf" ]; then
    L2TP_POOL=$(grep "ip range" /etc/xl2tpd/xl2tpd.conf 2>/dev/null | awk -F'=' '{print $2}' | tr -d ' ')
    L2TP_LOCAL=$(grep "local ip" /etc/xl2tpd/xl2tpd.conf 2>/dev/null | awk -F'=' '{print $2}' | tr -d ' ')
    echo "L2TP IP池: ${L2TP_POOL:-未配置}"
    echo "L2TP本地IP: ${L2TP_LOCAL:-未配置}"
    
    # 推断网络段
    if [ -n "$L2TP_POOL" ]; then
        FIRST_IP=$(echo "$L2TP_POOL" | cut -d'-' -f1)
        if [[ "$FIRST_IP" =~ ^192\.168\.([0-9]+)\. ]]; then
            SUBNET=$(echo "$FIRST_IP" | cut -d'.' -f3)
            echo "L2TP网络段: 192.168.$SUBNET.0/24"
        fi
    fi
else
    echo "L2TP配置: 未找到 /etc/xl2tpd/xl2tpd.conf"
fi

if [ -f "/etc/ppp/options.xl2tpd" ]; then
    DNS1=$(grep "ms-dns" /etc/ppp/options.xl2tpd 2>/dev/null | head -1 | awk '{print $2}')
    DNS2=$(grep "ms-dns" /etc/ppp/options.xl2tpd 2>/dev/null | tail -1 | awk '{print $2}')
    [ "$DNS1" = "$DNS2" ] && DNS2=""
    echo "主DNS: ${DNS1:-未配置}"
    [ -n "$DNS2" ] && echo "备用DNS: $DNS2"
else
    echo "DNS配置: 未找到 /etc/ppp/options.xl2tpd"
fi
echo ""

# 4. 服务状态
echo -e "${CYAN}[服务状态]${NC}"
SERVICES=("ipsec" "xl2tpd" "fail2ban")
for service in "${SERVICES[@]}"; do
    if systemctl is-active --quiet "$service" 2>/dev/null; then
        echo -e "$service: ${GREEN}运行中${NC}"
    else
        echo -e "$service: ${YELLOW}未运行${NC}"
    fi
done
echo ""

# 5. 端口监听
echo -e "${CYAN}[端口监听]${NC}"
PORTS=("500" "4500" "1701")
PORT_NAMES=("IKE" "NAT-T" "L2TP")
if command -v ss >/dev/null 2>&1; then
    for i in "${!PORTS[@]}"; do
        port="${PORTS[$i]}"
        name="${PORT_NAMES[$i]}"
        if ss -ulnp 2>/dev/null | grep -q ":$port "; then
            echo -e "UDP $port ($name): ${GREEN}监听中${NC}"
        else
            echo -e "UDP $port ($name): ${YELLOW}未监听${NC}"
        fi
    done
else
    echo "无法检查端口状态 (缺少ss命令)"
fi
echo ""

# 6. 防火墙状态
echo -e "${CYAN}[防火墙状态]${NC}"
if systemctl is-active --quiet firewalld 2>/dev/null; then
    echo -e "firewalld: ${GREEN}运行中${NC}"
elif systemctl is-active --quiet iptables 2>/dev/null; then
    echo -e "iptables: ${GREEN}运行中${NC}"
elif systemctl is-active --quiet nftables 2>/dev/null; then
    echo -e "nftables: ${GREEN}运行中${NC}"
else
    echo -e "防火墙: ${YELLOW}状态未知${NC}"
fi

# IP转发状态
IP_FORWARD=$(cat /proc/sys/net/ipv4/ip_forward 2>/dev/null)
if [ "$IP_FORWARD" = "1" ]; then
    echo -e "IP转发: ${GREEN}已启用${NC}"
else
    echo -e "IP转发: ${YELLOW}未启用${NC}"
fi
echo ""

# 7. 配置文件状态
echo -e "${CYAN}[配置文件]${NC}"
CONFIG_FILES=(
    "/etc/ipsec.conf:IPsec配置"
    "/etc/ipsec.secrets:IPsec密钥"
    "/etc/xl2tpd/xl2tpd.conf:L2TP配置"
    "/etc/ppp/options.xl2tpd:PPP选项"
    "/etc/ppp/chap-secrets:用户认证"
)

for item in "${CONFIG_FILES[@]}"; do
    file="${item%%:*}"
    desc="${item##*:}"
    if [ -f "$file" ]; then
        size=$(stat -c%s "$file" 2>/dev/null)
        echo -e "$desc: ${GREEN}存在${NC} (${size}字节)"
    else
        echo -e "$desc: ${YELLOW}不存在${NC}"
    fi
done
echo ""

# 8. 环境变量
echo -e "${CYAN}[环境变量]${NC}"
ENV_VARS=(
    "VPN_IPSEC_PSK"
    "VPN_USER" 
    "VPN_PASSWORD"
    "VPN_PUBLIC_IP"
    "VPN_DNS_SRV1"
    "VPN_DNS_SRV2"
    "VPN_L2TP_NET"
    "VPN_L2TP_LOCAL"
    "VPN_L2TP_POOL"
    "YOUR_IPSEC_PSK"
    "YOUR_USERNAME"
    "YOUR_PASSWORD"
)

for var in "${ENV_VARS[@]}"; do
    value="${!var}"
    if [ -n "$value" ]; then
        echo -e "$var: ${GREEN}$value${NC}"
    else
        echo -e "$var: ${YELLOW}未设置${NC}"
    fi
done
echo ""

# 9. 快速连接信息
echo -e "${CYAN}[连接信息摘要]${NC}"
echo "服务器IP: ${PUBLIC_IP:-<检测失败>}"
echo "协议类型: L2TP/IPsec"
echo "IPsec PSK: ${PSK:-<未读取到>}"
echo "用户名: ${USERNAME:-<未读取到>}"
echo "密码: ${PASSWORD:-<未读取到>}"
echo ""

echo -e "${BLUE}=== 显示完成 ===${NC}"
echo "详细报告: bash show_all_variables.sh"
echo "保存输出: bash $0 > vpn_vars.txt"

#!/bin/bash
#
# VPN设置脚本输出测试工具
# 用于验证 vpnsetup_centos.sh 脚本的配置文件和变量设置
#
# 使用方法: sudo bash vpn_setup_test_output.sh
#

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出函数
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# 检查文件是否存在并显示内容
check_file() {
    local file_path="$1"
    local description="$2"
    
    echo ""
    print_info "检查文件: $description"
    echo "文件路径: $file_path"
    
    if [ -f "$file_path" ]; then
        print_success "文件存在"
        echo "文件大小: $(stat -c%s "$file_path") 字节"
        echo "修改时间: $(stat -c%y "$file_path")"
        echo "文件权限: $(stat -c%a "$file_path")"
        echo ""
        echo "--- 文件内容 ---"
        cat "$file_path" 2>/dev/null || echo "无法读取文件内容"
        echo "--- 文件内容结束 ---"
    else
        print_error "文件不存在"
    fi
}

# 检查目录是否存在
check_directory() {
    local dir_path="$1"
    local description="$2"
    
    echo ""
    print_info "检查目录: $description"
    echo "目录路径: $dir_path"
    
    if [ -d "$dir_path" ]; then
        print_success "目录存在"
        echo "目录权限: $(stat -c%a "$dir_path")"
        echo "目录内容:"
        ls -la "$dir_path" 2>/dev/null || echo "无法列出目录内容"
    else
        print_error "目录不存在"
    fi
}

# 检查服务状态
check_service() {
    local service_name="$1"
    
    echo ""
    print_info "检查服务: $service_name"
    
    if systemctl is-active --quiet "$service_name"; then
        print_success "$service_name 服务正在运行"
    else
        print_warning "$service_name 服务未运行"
    fi
    
    if systemctl is-enabled --quiet "$service_name"; then
        print_success "$service_name 服务已启用开机自启"
    else
        print_warning "$service_name 服务未启用开机自启"
    fi
    
    echo "服务状态详情:"
    systemctl status "$service_name" --no-pager -l 2>/dev/null || echo "无法获取服务状态"
}

# 检查网络配置
check_network() {
    echo ""
    print_info "检查网络配置"
    
    echo "IP转发状态:"
    cat /proc/sys/net/ipv4/ip_forward 2>/dev/null || echo "无法读取IP转发状态"
    
    echo ""
    echo "网络接口信息:"
    ip addr show 2>/dev/null || ifconfig 2>/dev/null || echo "无法获取网络接口信息"
    
    echo ""
    echo "路由表:"
    ip route show 2>/dev/null || route -n 2>/dev/null || echo "无法获取路由信息"
}

# 检查防火墙规则
check_firewall() {
    echo ""
    print_info "检查防火墙规则"
    
    if command -v iptables >/dev/null 2>&1; then
        echo "IPTables规则:"
        iptables -L -n -v 2>/dev/null || echo "无法获取iptables规则"
        echo ""
        echo "NAT规则:"
        iptables -t nat -L -n -v 2>/dev/null || echo "无法获取NAT规则"
    fi
    
    if command -v nft >/dev/null 2>&1; then
        echo ""
        echo "NFTables规则:"
        nft list ruleset 2>/dev/null || echo "无法获取nftables规则"
    fi
}

# 检查进程
check_processes() {
    echo ""
    print_info "检查VPN相关进程"
    
    echo "IPsec进程:"
    ps aux | grep -E "(ipsec|pluto)" | grep -v grep || echo "未找到IPsec进程"
    
    echo ""
    echo "xl2tpd进程:"
    ps aux | grep xl2tpd | grep -v grep || echo "未找到xl2tpd进程"
    
    echo ""
    echo "fail2ban进程:"
    ps aux | grep fail2ban | grep -v grep || echo "未找到fail2ban进程"
}

# 显示环境变量
show_variables() {
    echo ""
    print_info "显示VPN相关环境变量"
    
    echo "当前设置的VPN相关环境变量:"
    env | grep -E "^VPN_|^YOUR_" | sort || echo "未找到VPN相关环境变量"
}

# 主函数
main() {
    print_header "VPN设置脚本输出测试报告"
    
    echo "测试时间: $(date)"
    echo "操作系统: $(cat /etc/redhat-release 2>/dev/null || echo '未知')"
    echo "内核版本: $(uname -r)"
    echo ""
    
    # 检查主要配置文件
    print_header "检查主要配置文件"
    check_file "/etc/ipsec.conf" "IPsec主配置文件"
    check_file "/etc/ipsec.secrets" "IPsec预共享密钥文件"
    check_file "/etc/xl2tpd/xl2tpd.conf" "xl2tpd配置文件"
    check_file "/etc/ppp/options.xl2tpd" "PPP选项文件"
    check_file "/etc/ppp/chap-secrets" "CHAP认证密钥文件"
    check_file "/etc/ipsec.d/passwd" "IPsec用户密码文件"
    
    # 检查系统配置文件
    print_header "检查系统配置文件"
    check_file "/etc/sysctl.conf" "内核参数配置"
    check_file "/etc/sysconfig/iptables" "IPTables规则文件"
    check_file "/etc/sysconfig/nftables.conf" "NFTables规则文件"
    check_file "/etc/fail2ban/jail.local" "Fail2Ban配置"
    check_file "/etc/rc.local" "开机启动脚本"
    check_file "/etc/firewalld/firewalld.conf" "Firewalld配置"
    check_file "/etc/dhcp/dhclient.conf" "DHCP客户端配置"
    check_file "/etc/crypto-policies/back-ends/nss.config" "NSS加密策略配置"
    
    # 检查目录和脚本
    print_header "检查目录和辅助脚本"
    check_directory "/opt/src" "源码目录"
    check_directory "/etc/ipsec.d" "IPsec配置目录"
    check_file "/opt/src/ikev2.sh" "IKEv2管理脚本"
    check_file "/opt/src/addvpnuser.sh" "添加VPN用户脚本"
    check_file "/opt/src/delvpnuser.sh" "删除VPN用户脚本"
    check_file "/usr/bin/ikev2.sh" "IKEv2脚本符号链接"
    check_file "/usr/bin/addvpnuser.sh" "添加用户脚本符号链接"
    check_file "/usr/bin/delvpnuser.sh" "删除用户脚本符号链接"
    
    # 检查服务状态
    print_header "检查服务状态"
    check_service "ipsec"
    check_service "xl2tpd"
    check_service "fail2ban"
    check_service "firewalld"
    check_service "nftables"
    check_service "iptables"
    
    # 检查网络配置
    print_header "检查网络配置"
    check_network
    
    # 检查防火墙规则
    print_header "检查防火墙规则"
    check_firewall
    
    # 检查进程
    print_header "检查进程状态"
    check_processes
    
    # 显示环境变量
    print_header "环境变量信息"
    show_variables
    
    print_header "测试完成"
    echo "报告生成时间: $(date)"
}

# 检查是否以root权限运行
if [ "$(id -u)" != 0 ]; then
    print_error "此脚本需要root权限运行。请使用 'sudo bash $0'"
    exit 1
fi

# 检查备份文件
check_backups() {
    echo ""
    print_info "检查配置文件备份"

    local backup_files=(
        "/etc/ipsec.conf.old-*"
        "/etc/ipsec.secrets.old-*"
        "/etc/xl2tpd/xl2tpd.conf.old-*"
        "/etc/ppp/options.xl2tpd.old-*"
        "/etc/ppp/chap-secrets.old-*"
        "/etc/ipsec.d/passwd.old-*"
        "/etc/sysctl.conf.old-*"
        "/etc/sysconfig/iptables.old-*"
        "/etc/sysconfig/nftables.conf.old-*"
        "/etc/rc.local.old-*"
        "/etc/dhcp/dhclient.conf.old-*"
    )

    for pattern in "${backup_files[@]}"; do
        local files=($(ls $pattern 2>/dev/null))
        if [ ${#files[@]} -gt 0 ]; then
            for file in "${files[@]}"; do
                print_success "找到备份文件: $file"
                echo "  大小: $(stat -c%s "$file" 2>/dev/null || echo '未知') 字节"
                echo "  时间: $(stat -c%y "$file" 2>/dev/null || echo '未知')"
            done
        fi
    done
}

# 检查端口监听
check_ports() {
    echo ""
    print_info "检查VPN相关端口监听状态"

    local ports=(500 4500 1701)

    for port in "${ports[@]}"; do
        echo "检查UDP端口 $port:"
        if command -v ss >/dev/null 2>&1; then
            ss -ulnp | grep ":$port " || echo "  端口 $port 未监听"
        elif command -v netstat >/dev/null 2>&1; then
            netstat -ulnp | grep ":$port " || echo "  端口 $port 未监听"
        else
            echo "  无法检查端口状态（缺少ss或netstat命令）"
        fi
        echo ""
    done
}

# 检查日志文件
check_logs() {
    echo ""
    print_info "检查VPN相关日志"

    local log_files=(
        "/var/log/secure"
        "/var/log/messages"
        "/var/log/pluto.log"
        "/var/log/xl2tpd.log"
        "/var/log/fail2ban.log"
    )

    for log_file in "${log_files[@]}"; do
        if [ -f "$log_file" ]; then
            print_success "日志文件存在: $log_file"
            echo "  大小: $(stat -c%s "$log_file") 字节"
            echo "  最后修改: $(stat -c%y "$log_file")"
            echo "  最近10行VPN相关日志:"
            tail -n 10 "$log_file" | grep -i -E "(ipsec|xl2tpd|vpn|l2tp)" | head -5 || echo "  未找到相关日志"
        else
            print_warning "日志文件不存在: $log_file"
        fi
        echo ""
    done
}

# 生成配置摘要
generate_summary() {
    echo ""
    print_header "配置摘要"

    # 提取关键配置信息
    if [ -f "/etc/ipsec.conf" ]; then
        echo "IPsec配置摘要:"
        grep -E "(leftid|virtual-private)" /etc/ipsec.conf 2>/dev/null | sed 's/^/  /'
    fi

    if [ -f "/etc/xl2tpd/xl2tpd.conf" ]; then
        echo ""
        echo "L2TP配置摘要:"
        grep -E "(ip range|local ip)" /etc/xl2tpd/xl2tpd.conf 2>/dev/null | sed 's/^/  /'
    fi

    if [ -f "/etc/ppp/options.xl2tpd" ]; then
        echo ""
        echo "PPP DNS配置:"
        grep "ms-dns" /etc/ppp/options.xl2tpd 2>/dev/null | sed 's/^/  /'
    fi

    if [ -f "/etc/ppp/chap-secrets" ]; then
        echo ""
        echo "VPN用户数量:"
        local user_count=$(grep -v "^#" /etc/ppp/chap-secrets 2>/dev/null | grep -v "^$" | wc -l)
        echo "  配置的用户数: $user_count"
    fi
}

# 运行连接测试
run_connection_test() {
    echo ""
    print_info "运行VPN连接测试"

    # 检查IPsec状态
    if command -v ipsec >/dev/null 2>&1; then
        echo "IPsec状态:"
        ipsec status 2>/dev/null || echo "  无法获取IPsec状态"
        echo ""

        echo "IPsec验证:"
        ipsec verify 2>/dev/null || echo "  无法运行IPsec验证"
    fi

    # 检查xl2tpd状态
    echo ""
    echo "xl2tpd进程检查:"
    if pgrep xl2tpd >/dev/null; then
        print_success "xl2tpd进程正在运行"
    else
        print_error "xl2tpd进程未运行"
    fi
}

# 运行主函数
main "$@"

# 在主函数中添加新的检查
main() {
    print_header "VPN设置脚本输出测试报告"

    echo "测试时间: $(date)"
    echo "操作系统: $(cat /etc/redhat-release 2>/dev/null || echo '未知')"
    echo "内核版本: $(uname -r)"
    echo ""

    # 检查主要配置文件
    print_header "检查主要配置文件"
    check_file "/etc/ipsec.conf" "IPsec主配置文件"
    check_file "/etc/ipsec.secrets" "IPsec预共享密钥文件"
    check_file "/etc/xl2tpd/xl2tpd.conf" "xl2tpd配置文件"
    check_file "/etc/ppp/options.xl2tpd" "PPP选项文件"
    check_file "/etc/ppp/chap-secrets" "CHAP认证密钥文件"
    check_file "/etc/ipsec.d/passwd" "IPsec用户密码文件"

    # 检查系统配置文件
    print_header "检查系统配置文件"
    check_file "/etc/sysctl.conf" "内核参数配置"
    check_file "/etc/sysconfig/iptables" "IPTables规则文件"
    check_file "/etc/sysconfig/nftables.conf" "NFTables规则文件"
    check_file "/etc/fail2ban/jail.local" "Fail2Ban配置"
    check_file "/etc/rc.local" "开机启动脚本"
    check_file "/etc/firewalld/firewalld.conf" "Firewalld配置"
    check_file "/etc/dhcp/dhclient.conf" "DHCP客户端配置"
    check_file "/etc/crypto-policies/back-ends/nss.config" "NSS加密策略配置"

    # 检查目录和脚本
    print_header "检查目录和辅助脚本"
    check_directory "/opt/src" "源码目录"
    check_directory "/etc/ipsec.d" "IPsec配置目录"
    check_file "/opt/src/ikev2.sh" "IKEv2管理脚本"
    check_file "/opt/src/addvpnuser.sh" "添加VPN用户脚本"
    check_file "/opt/src/delvpnuser.sh" "删除VPN用户脚本"
    check_file "/usr/bin/ikev2.sh" "IKEv2脚本符号链接"
    check_file "/usr/bin/addvpnuser.sh" "添加用户脚本符号链接"
    check_file "/usr/bin/delvpnuser.sh" "删除用户脚本符号链接"

    # 检查备份文件
    print_header "检查备份文件"
    check_backups

    # 检查服务状态
    print_header "检查服务状态"
    check_service "ipsec"
    check_service "xl2tpd"
    check_service "fail2ban"
    check_service "firewalld"
    check_service "nftables"
    check_service "iptables"

    # 检查端口监听
    print_header "检查端口监听"
    check_ports

    # 检查网络配置
    print_header "检查网络配置"
    check_network

    # 检查防火墙规则
    print_header "检查防火墙规则"
    check_firewall

    # 检查进程
    print_header "检查进程状态"
    check_processes

    # 检查日志
    print_header "检查日志文件"
    check_logs

    # 显示环境变量
    print_header "环境变量信息"
    show_variables

    # 生成配置摘要
    generate_summary

    # 运行连接测试
    print_header "连接测试"
    run_connection_test

    print_header "测试完成"
    echo "报告生成时间: $(date)"
    echo ""
    print_info "如需保存此报告，请运行: sudo bash $0 > vpn_test_report.txt"
}
